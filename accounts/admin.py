import ast

import openpyxl
import os
import subprocess

from django.contrib import admin, messages
from rangefilter.filters import DateRangeFilter
from accounts.models import *
from accounts.helpers.helper_func import get_week_start_and_end_datetime

from import_export import resources, fields, widgets
from import_export.admin import ImportExportModelAdmin

from django.http import HttpResponse
from openpyxl.styles import Font, PatternFill, Border, Side, Alignment

###############################################################################
# RESOURCES
from main.models import Region
from main.helper.logging_utils import log_info, log_debug, log_warning, log_error, log_critical


class AccountSystemResource(resources.ModelResource):
    class Meta:
        model = AccountSystem


class AccountCreationFailureResource(resources.ModelResource):
    class Meta:
        model = AccountCreationFailure


class WalletSystemResource(resources.ModelResource):
    class Meta:
        model = WalletSystem


class MerchantDisbursementsResource(resources.ModelResource):
    class Meta:
        model = MerchantDisbursements


class EscrowResource(resources.ModelResource):
    class Meta:
        model = Escrow


class TransactionResource(resources.ModelResource):
    class Meta:
        model = Transaction


class BeneficiariesResource(resources.ModelResource):
    class Meta:
        model = Beneficiaries


class TransferVerificationObjectResource(resources.ModelResource):
    class Meta:
        model = TransferVerificationObject


class DeletedReferenceResource(resources.ModelResource):
    class Meta:
        model = DeletedReference


class RawPayStackResource(resources.ModelResource):
    class Meta:
        model = RawPayStack


class PayStackTransactionResource(resources.ModelResource):
    class Meta:
        model = PayStackTransaction


class CreditCardDetailResource(resources.ModelResource):
    class Meta:
        model = CreditCardDetail


class WEMACallBackResource(resources.ModelResource):
    class Meta:
        model = WEMACallBack


class LedgerBalanceResource(resources.ModelResource):
    class Meta:
        model = LedgerBalance


class LedgerHistoryModelResource(resources.ModelResource):
    class Meta:
        model = LedgerHistoryModel


class AirtimeToPinParentResource(resources.ModelResource):
    class Meta:
        model = AirtimeToPinParent


class AirtimeToPinObjectResource(resources.ModelResource):
    class Meta:
        model = AirtimeToPinObject


class AccountInflowPayloadResource(resources.ModelResource):
    class Meta:
        model = AccountInflowPayload


class AccountOutflowsTrailResource(resources.ModelResource):
    class Meta:
        model = AccountOutflowsTrail


class InAppTransactionNotificationResource(resources.ModelResource):
    class Meta:
        model = InAppTransactionNotification


class DebitCreditRecordOnAccountResource(resources.ModelResource):
    class Meta:
        model = DebitCreditRecordOnAccount


class BillsPaymentConstantResource(resources.ModelResource):
    class Meta:
        model = BillsPaymentConstant


class CommissionsRecordResource(resources.ModelResource):
    class Meta:
        model = CommissionsRecord


class AllBankListResource(resources.ModelResource):
    class Meta:
        model = AllBankList


class GeneralDataDumpResource(resources.ModelResource):
    class Meta:
        model = GeneralDataDump


class BillsPaymentDumpDataResource(resources.ModelResource):
    class Meta:
        model = BillsPaymentDumpData


class SendMoneyDumpDataResource(resources.ModelResource):
    class Meta:
        model = SendMoneyDumpData


class TransactionPerformanceResource(resources.ModelResource):
    class Meta:
        model = TransactionPerformance


class CashOutChargeBandResource(resources.ModelResource):
    class Meta:
        model = CashOutChargeBand


class OtherCommissionsRecordResource(resources.ModelResource):
    class Meta:
        model = OtherCommissionsRecord


class UploadSendMoneyDataResource(resources.ModelResource):
    class Meta:
        model = UploadSendMoneyData


class OutOfBookTransferResource(resources.ModelResource):
    class Meta:
        model = OutOfBookTransfer


class FundingRecordResource(resources.ModelResource):
    class Meta:
        model = FundingRecord


class DailyFundingRecordResource(resources.ModelResource):
    class Meta:
        model = DailyFundingRecord


class WeekHistoryResource(resources.ModelResource):
    class Meta:
        model = WeekHistory


class DailyRewardHistoryResource(resources.ModelResource):
    class Meta:
        model = DailyRewardHistory


class MonthlyRewardHistoryResource(resources.ModelResource):
    class Meta:
        model = MonthlyRewardHistory


class TransactionRewardResource(resources.ModelResource):
    class Meta:
        model = TransactionReward


class LeaderBoardPOSResource(resources.ModelResource):
    class Meta:
        model = LeaderBoardPOS


class LeaderBoardMobileResource(resources.ModelResource):
    class Meta:
        model = LeaderBoardMobile


class LeaderBoardResource(resources.ModelResource):
    class Meta:
        model = LeaderBoard


class POSRequestResource(resources.ModelResource):
    class Meta:
        model = POSRequest


class StartFundByUSSDTranResource(resources.ModelResource):
    class Meta:
        model = StartFundByUSSDTran


class DailyUptimeDowntimeRecordResource(resources.ModelResource):
    class Meta:
        model = DailyUptimeDowntimeRecord


class OtherAppTransNotifyResource(resources.ModelResource):
    class Meta:
        model = OtherAppTransNotify


class OtherServiceAccountSystemResource(resources.ModelResource):
    class Meta:
        model = OtherServiceAccountSystem


class ParallexTokenResource(resources.ModelResource):
    class Meta:
        model = ParallexToken


class QRCodeResource(resources.ModelResource):
    class Meta:
        model = QRCode


class UserOwnAccountResource(resources.ModelResource):
    class Meta:
        model = UserOwnAccount


class RequisitionGroupResource(resources.ModelResource):
    class Meta:
        model = RequisitionGroup


class QRCyberWebhookDataResource(resources.ModelResource):
    class Meta:
        model = QRCyberWebhookData


class AutoSweepRecurringChargeTableResource(resources.ModelResource):
    class Meta:
        model = AutoSweepRecurringChargeTable


class AutoSweepRecurringChargeHistoryResource(resources.ModelResource):
    class Meta:
        model = AutoSweepRecurringChargeHistory


class WithdrawalByUSSDNotificationDataResource(resources.ModelResource):
    class Meta:
        model = WithdrawalByUSSDNotificationData


class OpeningClosingBalanceResource(resources.ModelResource):
    class Meta:
        model = OpeningClosingBalance


class ParallexDumpDataResource(resources.ModelResource):
    class Meta:
        model = ParallexDumpData


class PromoCodeDataResource(resources.ModelResource):
    class Meta:
        model = PromoCodeData


class ReconciledWalletDataResource(resources.ModelResource):
    class Meta:
        model = ReconciledWalletData


class UserDebtResource(resources.ModelResource):
    class Meta:
        model = UserDebt


class PromoCodeDeciderResource(resources.ModelResource):
    class Meta:
        model = PromoCodeDecider


class UserAccountNumCountResource(resources.ModelResource):
    class Meta:
        model = UserAccountNumCount


class LessMinimumAmountCommResource(resources.ModelResource):
    class Meta:
        model = LessMinimumAmountComm


class LienResource(resources.ModelResource):
    class Meta:
        model = Lien


class PendingTransactionResource(resources.ModelResource):
    class Meta:
        model = PendingTransaction


class UserPasswordManualSetResource(resources.ModelResource):
    class Meta:
        model = UserPasswordManualSet


class TransactionLimitLogResource(resources.ModelResource):
    class Meta:
        model = TransactionLimitLog


class TransactionDisputeResource(resources.ModelResource):
    class Meta:
        model = TransactionDispute


#######################################################################
# ADMINS


class AccountSystemAdmin(ImportExportModelAdmin):
    autocomplete_fields = ['user', 'wallet']
    resource_class = AccountSystemResource
    search_fields = ['user__email', 'account_number', 'payload']
    list_filter = (
        ('account_provider', 'account_type', 'date_created')
    )

    if settings.ENVIRONMENT != "development":
        exclude = ('payload',)
        readonly_fields = ('other_balance',)
    else:
        exclude = ('payload',)

    def get_list_display(self, request):
        return [field.name for field in self.model._meta.concrete_fields if field.name not in self.exclude]

    # def get_list_display(self, request):
    #     return [field.name if field.name not in ["initial_payload", "payload"] else "account_number" for field in self.model._meta.concrete_fields]


class AccountCreationFailureAdmin(ImportExportModelAdmin):
    resource_class = AccountCreationFailureResource

    # search_fields = ['admin_user__email', 'account_affected', 'action', 'date_created']
    # list_filter = (
    #     ('date_created',)
    # )
    # date_hierarchy = 'date_created'

    def get_list_display(self, request):
        return [field.name for field in self.model._meta.concrete_fields]


class WalletSystemResourceAdmin(ImportExportModelAdmin):
    autocomplete_fields = ['user']
    resource_class = WalletSystemResource
    search_fields = ['user__email']
    list_filter = (
        ('wallet_type', 'is_active', 'user__type_of_user', 'user__is_fraud')
    )

    if settings.ENVIRONMENT != "development":
        readonly_fields = ('available_balance',)

    def get_list_display(self, request):
        return [field.name for field in self.model._meta.concrete_fields]

    @admin.action(description="Create accounts for selected users")
    def create_accounts_action(self, request, queryset):
        success_count = 0
        for obj in queryset:
            user = obj.user
            if obj.wallet_type != "COLLECTION":
                continue
            try:
                AccountSystem.create_accounts(user=user, wallet=obj)
                success_count += 1
            except Exception as e:
                self.message_user(
                    request,
                    f"Failed to create account for {user.email}: {e}",
                    level=messages.ERROR
                )
        if success_count:
            self.message_user(
                request,
                f"Successfully created accounts for {success_count} users.",
                level=messages.SUCCESS
            )

    # Register the action
    actions = ['create_accounts_action']


class MerchantDisbursementsResourceAdmin(ImportExportModelAdmin):
    resource_class = MerchantDisbursementsResource

    def get_list_display(self, request):
        return [field.name for field in self.model._meta.concrete_fields]


class EscrowResourceAdmin(ImportExportModelAdmin):
    autocomplete_fields = ['user']
    resource_class = EscrowResource
    search_fields = ['user__email', 'escrow_id', 'send_by_card_rrn', 'customer_reference']
    list_filter = (
        ('transfer_type', 'date_created')
    )

    def get_list_display(self, request):
        return [field.name for field in self.model._meta.concrete_fields]

    def get_readonly_fields(self, request, obj=None):
        readonly_fields = [field.name for field in self.model._meta.concrete_fields]
        if request.user.groups.filter(name__in=['Tech Support']) != None \
                or request.user.email in ['<EMAIL>']:
            exclude_fields = ['internal_escrow', 'external_escrow', 'reversed']
            readonly_fields = [field for field in readonly_fields if field not in exclude_fields]

        if request.user.email in ['<EMAIL>', '<EMAIL>']:
            readonly_fields = []

        return readonly_fields

    actions = ["resolve_buddy_transaction", ]

    def get_actions(self, request):

        actions = super().get_actions(request)

        allowed_users = ['<EMAIL>', ]  # add more emails as needed

        if (request.user.is_superuser) or (request.user.email in allowed_users) or (request.user.groups.filter(name__in=['Tech Support']).exists()):
            return actions
        else:
            actions.pop('resolve_buddy_transaction', None)

        return actions

    @admin.action(description="Resolve buddy transaction")
    def resolve_buddy_transaction(self, request, queryset):
        # for obj in queryset:
        #     buddy_transfer_resolving_func(obj.id)

        for obj in queryset:
            try:
                # Call the management command using subprocess
                venv_python = "/home/<USER>/AgencyBanking/venv/bin/python3"
                manage_py_path = "/home/<USER>/AgencyBanking/manage.py"  # Adjust this path if necessary

                # Call the management command using subprocess
                subprocess.run(
                    [venv_python, manage_py_path, "verf_buddy_trans", f"--debcred={obj.debit_credit_record_id}"],
                    capture_output=True,
                    text=True,
                    check=True,
                    cwd=os.path.dirname(manage_py_path)
                )

                subprocess.run(
                    [venv_python, manage_py_path, "verf_buddy_send", f"--escrow={obj.id}"],
                    capture_output=True,
                    text=True,
                    check=True,
                    cwd=os.path.dirname(manage_py_path)
                )

                # messages.success(request, f"Successfully resolved transaction ID: {obj.id}")
            except subprocess.CalledProcessError as e:
                # Handle errors from the command
                error_message = f"Failed to resolve transaction ID {obj.id}. Error: {e.stderr}"
                messages.error(request, error_message)

        messages.success(request, "successful!")


class TransactionResourceAdmin(ImportExportModelAdmin):
    list_per_page = 55
    # autocomplete_fields = ['user', 'is_other_account_owner']
    resource_class = TransactionResource
    # search_fields = ['user__email', 'liberty_reference', 'unique_reference', "transaction_id", "escrow_id"]
    search_fields = ['user__email', 'transaction_id']

    # exclude = ('payload', 'float_before', 'float_after', 'float_bal_before', 'float_bal_after', 'user_email', 'sales_rep', 'extra_fee', 'final_liberty_rev',
    #            'liberty_profit', 'ro_profit', 'agent_profit', 'transaction_mode', 'send_money_by_card', 'send_by_card_rrn', 'wallet_id', 'account_id', 'wallet_type',
    #             'is_verified', 'transaction_commission_id', 'spread', 'total_amount_charged', 'total_amount_sent_out', 'total_amount_received', 'total_amount_resolved', 'source_account_id', 'source_wallet_id', 'source_wallet_type', 'beneficiary_wallet_id', 'beneficiary_wallet_type'
    # )
    exclude = ('payload',)
    list_filter = [
        ("date_created", DateRangeFilter), 'status', 'transaction_type', 'transaction_leg', 'date_created', 'is_reversed', 'sales_rep',
        'callback_sent', 'is_other_account', 'user__type_of_user'
    ]
    date_hierarchy = 'date_created'
    # list_display = [""]

    list_display = [
        "id", "date_created", "last_updated", "transaction_type", "amount", "status", "user_email", "beneficiary_account_name", "beneficiary_nuban",
        "transaction_id", "transaction_leg", "escrow_id", "liberty_reference", "unique_reference", "narration"
    ]

    # def get_list_display(self, request):
    #     return [field.name for field in self.model._meta.concrete_fields]

    # def get_queryset(self, request):
    #     start_date = datetime(2023, 7, 1)
    #     qs = Transaction.objects.select_related('user').filter(date_created__gte=start_date)

    #     return qs

    def get_queryset(self, request):
        qs = Transaction.objects \
            .only(*self.list_display)

        return qs

    actions = ["export_transactions"]

    def export_transactions(self, request, queryset):
        """
        Export weekly and monthly transaction reports for merchants.
        """
        workbook = openpyxl.Workbook()
        weekly_sheet = workbook.active
        weekly_sheet.title = "Weekly Revenue Monitoring"
        monthly_sheet = workbook.create_sheet(title="Monthly Agent Summary")

        header_fill_yellow = PatternFill(start_color="FFFF00", end_color="FFFF00", fill_type="solid")
        header_fill_green = PatternFill(start_color="C6EFCE", end_color="C6EFCE", fill_type="solid")
        bold_font = Font(bold=True)
        border = Border(left=Side(style="thin"), right=Side(style="thin"), top=Side(style="thin"), bottom=Side(style="thin"))
        center_align = Alignment(horizontal="center")

        weekly_headers = ["Merchant", "Account Type", "Week", "Transaction Count", "Total Volume", "Gross Revenue"]
        weekly_sheet.append(weekly_headers)

        monthly_headers = ["Merchant", "Account Type", "Month", "Transaction Count", "Total Volume", "Gross Revenue"]
        monthly_sheet.append(monthly_headers)

        for col_num, col_name in enumerate(weekly_headers, 1):
            cell = weekly_sheet.cell(row=1, column=col_num)
            cell.font = bold_font
            cell.fill = header_fill_yellow if col_name in ["Merchant", "Transaction Count", "Total Volume"] else header_fill_green
            cell.alignment = center_align
            cell.border = border

        for col_num, col_name in enumerate(monthly_headers, 1):
            cell = monthly_sheet.cell(row=1, column=col_num)
            cell.font = bold_font
            cell.fill = header_fill_yellow if col_name in ["Merchant", "Transaction Count", "Total Volume"] else header_fill_green
            cell.alignment = center_align
            cell.border = border

        # transactions = queryset.filter(type_of_user="AGENT")
        weekly_data = {}
        monthly_data = {}

        for transaction in queryset:
            user = str(transaction.user.get_full_name()).capitalize()
            account_type = str(transaction.user.type_of_user).upper()
            # week = transaction.date_created.strftime("%Y-%W")
            week_start, week_end = get_week_start_and_end_datetime(transaction.date_created)
            week = str(week_start.strftime("%d %b")) + " - " + str(week_end.strftime("%d %b"))
            month = transaction.date_created.strftime("%B %Y")

            if (user, week) not in weekly_data:
                weekly_data[(user, week)] = {"count": 0, "total_volume": 0, "gross_revenue": 0}
            weekly_data[(user, week)]["count"] += 1
            weekly_data[(user, week)]["total_volume"] += transaction.amount
            weekly_data[(user, week)]["gross_revenue"] += transaction.amount * 0.02

            if (user, month) not in monthly_data:
                monthly_data[(user, month)] = {"count": 0, "total_volume": 0, "gross_revenue": 0}
            monthly_data[(user, month)]["count"] += 1
            monthly_data[(user, month)]["total_volume"] += transaction.amount
            monthly_data[(user, month)]["gross_revenue"] += transaction.amount * 0.02

        for (user, week), data in weekly_data.items():
            row = [user, account_type, week, data["count"], data["total_volume"], data["gross_revenue"]]
            weekly_sheet.append(row)

        for (user, month), data in monthly_data.items():
            row = [user, account_type, month, data["count"], data["total_volume"], data["gross_revenue"]]
            monthly_sheet.append(row)

        for sheet in [weekly_sheet, monthly_sheet]:
            for col in range(1, len(weekly_headers) + 1):
                sheet.column_dimensions[openpyxl.utils.get_column_letter(col)].width = 20
                for row in sheet.iter_rows(min_row=2, max_row=sheet.max_row, min_col=1, max_col=len(weekly_headers)):
                    for cell in row:
                        cell.alignment = center_align
                        cell.border = border
                        if isinstance(cell.value, (int, float)):
                            cell.number_format = "#,##0.00"

        response = HttpResponse(content_type="application/vnd.openxmlformats-officedocument.spreadsheetml.sheet")
        response["Content-Disposition"] = "attachment; filename=Transaction_Report.xlsx"
        workbook.save(response)
        return response

    export_transactions.short_description = "Export transactions to Excel"


class BeneficiariesResourceAdmin(ImportExportModelAdmin):
    resource_class = BeneficiariesResource
    search_fields = ['user__email', 'account_name', 'bank_name', 'account_number', 'phone_number']
    list_filter = (
        ('is_active', 'beneficiary_type', 'date_added', 'date_removed')
    )

    def get_list_display(self, request):
        return [field.name for field in self.model._meta.concrete_fields]


class TransferVerificationObjectResourceAdmin(ImportExportModelAdmin):
    def verify_transactions(self, request, queryset):
        queryset = queryset.filter(transaction_ver_status__in=["NOT_INITIATED", "PENDING", "NOT_FOUND"])

        for transaction_verf in queryset:
            transaction_verf: TransferVerificationObject
            liberty_reference = str(transaction_verf.liberty_reference)

            if transaction_verf.trans_status_code == "108":
                verify_trans = VFDBank.vfd_transaction_verification_handler(reference=liberty_reference)
                if verify_trans["status"] == "108":
                    transaction_verf.transaction_ver_status = "NOT_INITIATED"
                    transaction_verf.verification_payload = verify_trans
                    transaction_verf.save()

            else:
                verification_instance = dict(
                    transaction_instance=transaction_verf.transaction_instance,
                    user_id=transaction_verf.user_id,
                    user_email=transaction_verf.user_email,
                    account_provider=transaction_verf.account_provider,
                    transaction_leg=transaction_verf.transaction_leg,
                    transaction_type=transaction_verf.transaction_type,
                    timestamp=str(datetime.now()),
                    escrow_id=transaction_verf.escrow_id,
                    amount=transaction_verf.amount,
                    liberty_reference=liberty_reference,
                    is_test=True if settings.ENVIRONMENT == "development" else False
                )

                verf_pending_trans = TransferVerificationObject.create_verfication_check(verification_instance, instant=True)

        self.message_user(request, "Successfully Checked Transactions")

    verify_transactions.short_description = "Verify Transactions for selected items"

    def verify_commissions(self, request, queryset):
        not_data_list = queryset.filter(transaction_ver_status__in=["NOT_INITIATED"])
        pending_data_list = queryset.filter(transaction_ver_status__in=["PENDING"])

        TransferVerificationObject.not_initiated_func(not_data_list)
        TransferVerificationObject.pending_comms_func(pending_data_list)

        self.message_user(request, "Successfully Checked Commissions")

    verify_commissions.short_description = "Verify Commission for selected items"

    list_per_page = 55
    autocomplete_fields = ['transaction_instance']
    resource_class = TransferVerificationObjectResource
    search_fields = ["liberty_reference", "escrow_id", "user_email", ]
    list_filter = (
        (
        "transaction_ver_status", "transaction_leg", "date_added", "trans_status_code", "has_duplicate", "checked_for_duplicate", "duplicate_counted",
        "last_updated", "verify_type")
    )
    date_hierarchy = 'date_added'
    actions = [verify_transactions, verify_commissions]

    list_display = ["transaction_instance", "user_id", "user_email", "transaction_leg", "transaction_ver_status", "transaction_type",
                    "transaction_sub_type", "escrow_id", "amount", "liberty_reference", "unique_reference", "trans_status_code",
                    "is_finished_verification", "source_nuban", "beneficiary_nuban", "bank_code", "verification_payload", "date_added",
                    "last_updated"]

    def get_readonly_fields(self, request, obj=None):
        readonly_fields = [field.name for field in self.model._meta.concrete_fields]
        if request.user.groups.filter(name__in=['Tech Support']) != None \
                or request.user.email in ['<EMAIL>']:
            exclude_fields = ['transaction_ver_status', 'is_finished_verification']
            readonly_fields = [field for field in readonly_fields if field not in exclude_fields]

        if request.user.email in ['<EMAIL>', '<EMAIL>']:
            readonly_fields = []

        return readonly_fields

    # def transaction_instance_transaction_id(self, obj):
    #         return obj.transaction_instance.transaction_id

    # transaction_instance_transaction_id.short_description = "Transaction ID"

    # def get_queryset(self, request):
    #     queryset = super().get_queryset(request)
    #     return queryset.select_related('transaction_instance')
    # def get_list_display(self, request):
    #     return [field.name for field in self.model._meta.concrete_fields]

    # def get_queryset(self, request):
    #     start_date = datetime(2023, 7, 1)
    #     qs = TransferVerificationObject.objects.select_related('transaction_instance').filter(date_added__gte=start_date)

    #     return qs


class DeletedReferenceResourceAdmin(ImportExportModelAdmin):
    autocomplete_fields = ['transaction_instance']
    resource_class = DeletedReferenceResource
    search_fields = ["liberty_reference", "transaction_instance__transaction_id", "transaction_instance__liberty_reference"]
    list_filter = (
        ('trans_type', "date_created")
    )

    def get_list_display(self, request):
        return [field.name for field in self.model._meta.concrete_fields]


class RawPayStackResourceAdmin(ImportExportModelAdmin):
    resource_class = RawPayStackResource

    def get_list_display(self, request):
        return [field.name for field in self.model._meta.concrete_fields]


class PayStackTransactionResourceAdmin(ImportExportModelAdmin):
    resource_class = PayStackTransactionResource

    def get_list_display(self, request):
        return [field.name for field in self.model._meta.concrete_fields]


class CreditCardDetailResourceAdmin(ImportExportModelAdmin):
    resource_class = CreditCardDetailResource

    def get_list_display(self, request):
        return [field.name for field in self.model._meta.concrete_fields]


class WEMACallBackAdmin(ImportExportModelAdmin):
    resource_class = WEMACallBackResource

    def get_list_display(self, request):
        return [field.name for field in self.model._meta.concrete_fields]


class LedgerBalanceResourceAdmin(ImportExportModelAdmin):
    resource_class = LedgerBalanceResource

    def get_list_display(self, request):
        return [field.name for field in self.model._meta.concrete_fields]


class LedgerHistoryModelResourceAdmin(ImportExportModelAdmin):
    resource_class = LedgerHistoryModelResource

    def get_list_display(self, request):
        return [field.name for field in self.model._meta.concrete_fields]


class AirtimeToPinParentResourceAdmin(ImportExportModelAdmin):
    search_fields = ["user__email", "batch_id", "liberty_reference", "unique_reference"]
    resource_class = AirtimeToPinParentResource

    def get_list_display(self, request):
        return [field.name for field in self.model._meta.concrete_fields]


class AirtimeToPinObjectResourceAdmin(ImportExportModelAdmin):
    search_fields = ["biller_parent__batch_id", "liberty_reference", "unique_reference", "serial_number", "card_pin"]
    resource_class = AirtimeToPinObjectResource

    def get_list_display(self, request):
        return [field.name for field in self.model._meta.concrete_fields]


class AccountOutflowsTrailResourceAdmin(ImportExportModelAdmin):
    search_fields = ['payload', ]
    resource_class = AccountOutflowsTrailResource

    def get_list_display(self, request):
        return [field.name for field in self.model._meta.concrete_fields]


class AccountInflowPayloadResourceAdmin(ImportExportModelAdmin):
    search_fields = ['payload', 'account_num', 'unique_reference', 'transaction_reference']
    resource_class = AccountInflowPayloadResource
    actions = ["re_run_inflow_transactions", "resolve_pending_inflows"]

    def re_run_inflow_transactions(self, request, queryset):
        for instance in queryset:
            # Check if instance has transaction or credit debit record based on transaction ref or session_id
            acct_no = instance.account_num
            unique_ref = instance.unique_reference
            trans_ref = instance.transaction_reference
            amount = instance.amount
            session_id = instance.session_id
            provider = instance.account_provider

            if provider == "WEMA_BANK":
                provider = "WEMA"

            # Check if Transaction exists
            if Transaction.objects.filter(unique_reference=unique_ref).exists():
                self.message_user(request, f"Transaction already exist for {unique_ref}", level=messages.ERROR)

            # Check if DebitCreditRecordOnAccount exists
            if DebitCreditRecordOnAccount.objects.filter(unique_reference=unique_ref).exists():
                self.message_user(request, f"DebitCreditRecordOnAccount already exist for {unique_ref}", level=messages.ERROR)

            # Check if AccountSystem is multiple
            main_account = None
            acct_nos = AccountSystem.objects.filter(account_number=acct_no, account_type="COLLECTION").order_by("date_created")
            if acct_nos.exists():
                main_account = acct_nos.first()
            if len(acct_nos) > 1:
                main_account = acct_nos.first()
                other_accounts = acct_nos.exclude(id=main_account.id)
                other_accounts.delete()
            # Complete inflow
            if provider in ['WEMA', 'FIDELITY'] and main_account is not None:
                data_str = str(instance.payload).replace("null", "None").replace("false", "False").replace("true", "True")
                instance_payload = ast.literal_eval(data_str)

                payload = dict(
                    amount=amount,
                    unique_reference=unique_ref,
                    payment_reference=trans_ref,
                    transaction_reference=trans_ref,
                    user_nuban=instance_payload.get("recipient_account_number", None),
                    source_nuban=instance_payload.get("originator_account_number", None),
                    source_account_name=instance_payload.get("payer_account_name", None),
                    narration=instance_payload.get("narration", None),
                    source_bank_code=instance_payload.get("payer_bank_code", None),
                    account_provider=provider,
                    provider_fee=instance_payload.get("fee", 0),
                    transfer_status=instance_payload.get("settlement_status"),
                    provider_status=instance_payload.get("settlement_status"),
                    timestamp=instance_payload.get("paid_at"),
                    funding_payload=instance_payload,
                    float_inflow=False,
                    session_id=session_id
                )

                AccountSystem.inflow_handler(main_account.wallet.user_id, payload, provider)
            self.message_user(request, f"Inflow re-run successful for {unique_ref}", level=messages.SUCCESS)

    re_run_inflow_transactions.short_description = "RE-RUN TRANSACTION INFLOW FOR SELECTED ITEM(S)"

    def resolve_pending_inflows(self, request, queryset):
        from django.db import transaction
        try:
            with transaction.atomic():
                for inflow in queryset:
                    ref_number = inflow.unique_reference
                    amount = inflow.amount
                    payload = json.loads(inflow.payload)
                    source_account_name = payload.get("payer_account_name")
                    source_account_number = payload.get("originator_account_number")
                    transaction_instance = None
                    transaction_instance_qs = Transaction.objects.filter(
                        status="PENDING", amount=amount, unique_reference=ref_number, transaction_type="FUND_BANK_TRANSFER"
                    )
                    if transaction_instance_qs:
                        transaction_instance = transaction_instance_qs.first()

                    if transaction_instance is not None:
                        user = transaction_instance.user
                        wallet_instance = WalletSystem.get_wallet(user=user, from_wallet_type="COLLECTION")
                        commission = ConstantTable.calculate_fund_bank_transfer_fees(user, amount)

                        charges_on_inflow = 0
                        if user.is_paybox_merchant:
                            charges_on_inflow = deduct_commission_from_paybox_merchant(amount, source_account_number)

                        commission += charges_on_inflow
                        new_amount = amount - commission

                        user_balance_before = wallet_instance.available_balance if wallet_instance else 0.0

                        fund_wallet = WalletSystem.fund_balance(
                            user=user,
                            wallet=wallet_instance,
                            amount=new_amount,
                            trans_type="FUND_BANK_TRANSFER",
                            transaction_instance_id=transaction_instance.transaction_id,
                            unique_reference=ref_number
                        )

                        balance_after = fund_wallet['balance_after']
                        transaction_instance.balance_after = balance_after
                        transaction_instance.balance_before = user_balance_before
                        transaction_instance.status = "SUCCESSFUL"
                        transaction_instance.save()

                        create_electronic_levy_transaction(transaction_instance)

                        ##########################################################################################
                        # SEND OUT APP NOTIFICATION
                        receiver_not_token = user.firebase_key
                        receiver_not_title = "Payment Received"
                        receiver_not_body = f"You have recieved a CREDIT of N{amount} from {source_account_name}"
                        receiver_not_data = {"amount_sent": f"{amount}", "available_balance": f"{balance_after}"}

                        cloud_messaging.send_broadcast(
                            token=receiver_not_token, title=receiver_not_title, body=receiver_not_body, data=receiver_not_data
                        )

                        self.message_user(request, f"Successfully settled inflow for {transaction_instance.id}", level=messages.SUCCESS)
                    else:
                        self.message_user(request, f"Pending transaction not found fo this in-flow ID: {inflow.id}", level=messages.ERROR)

        except Exception as err:
            self.message_user(request, f"An error has occurred while running this action:\nError: {err}", level=messages.ERROR)

    resolve_pending_inflows.short_description = "RESOLVE PENDING TRANSACTION INFLOW FOR SELECTED ITEM(S)"

    def get_list_display(self, request):
        return [field.name for field in self.model._meta.concrete_fields]


class InAppTransactionNotificationResourceAdmin(ImportExportModelAdmin):
    resource_class = InAppTransactionNotificationResource

    def get_list_display(self, request):
        return [field.name for field in self.model._meta.concrete_fields]


class DebitCreditRecordOnAccountResourceAdmin(ImportExportModelAdmin):
    # readonly_fields = ["transaction_instance_id",]
    autocomplete_fields = ['user', 'wallet']
    search_fields = ["user__email", "transaction_instance_id", "reversal_trans_id", "unique_reference"]
    resource_class = DebitCreditRecordOnAccountResource
    list_filter = (
        ('entry', 'type_of_trans', 'date_created')
    )
    date_hierarchy = 'date_created'

    # Adding the custom action
    actions = ['reverse_truncated_debit']

    def get_list_display(self, request):
        return [field.name for field in self.model._meta.concrete_fields]

    # Custom action to reverse truncated debits
    def reverse_truncated_debit(self, request, queryset):
        for record in queryset:
            if record.entry != "DEBIT":
                self.message_user(request, f"RecordID: {record.id} is not a DebitType record", level=messages.ERROR)
            elif record.reversal_trans_id or record.transaction_instance_id:
                self.message_user(request, f"RecordID: {record.id} has a transaction/reversal instance", level=messages.ERROR)
            elif Transaction.objects.filter(debit_credit_record_id=record.id).exists():
                self.message_user(request, f"RecordID: {record.id} has a transaction instance", level=messages.ERROR)
            else:
                amount = record.amount
                sms_charge = WalletSystem.get_sms_charge(user=record.user, transaction_type="WALLET_DEBIT_REVERSAL", total_amount=amount)
                amount_to_reverse = amount - sms_charge
                wallet = record.wallet
                user = record.user
                reference = Transaction.create_liberty_reference("LGLP-DBT-REV")
                unique_ref = uuid.uuid4()
                # Create Reversal Record
                wdr, _ = WalletDebitReversal.objects.get_or_create(user=user, debit_credit=record, amount=amount)
                # Create Escrow
                escrow = Escrow.objects.create(
                    user=user, to_wallet_type=wallet.wallet_type, transfer_type="WALLET_DEBIT_REVERSAL", amount=amount_to_reverse,
                    total_amount_charged=amount, to_wallet_id=wallet.wallet_id,
                    is_for_reversal=True, liberty_reference=reference
                )
                # Create Transaction
                transaction_instance = Transaction.objects.create(
                    transaction_type="WALLET_DEBIT_REVERSAL", amount=amount_to_reverse, status="SUCCESSFUL", user=user,
                    user_full_name=user.get_full_name, user_email=user.email, escrow_id=escrow.escrow_id, unique_reference=unique_ref
                )
                # Fund balance
                funding = WalletSystem.fund_balance(
                    user=user, wallet=wallet, amount=amount_to_reverse, trans_type="WALLET_DEBIT_REVERSAL",
                    transaction_instance_id=transaction_instance.transaction_id, unique_reference=unique_ref
                )
                # Update records
                before_balance = float(funding.get("balance_before"))
                after_balance = float(funding.get("balance_after"))
                new_debit_credit_record = funding.get("record")
                transaction_instance.balance_before = before_balance
                transaction_instance.balance_after = after_balance
                transaction_instance.save()
                escrow.balance_before = before_balance
                escrow.balance_after = after_balance
                escrow.debit_credit_record_id = new_debit_credit_record.id
                escrow.save()
                wdr.transaction_id = transaction_instance.transaction_id
                wdr.failed_transaction_type = record.type_of_trans
                wdr.reversed = True
                wdr.save()
                record.reversal_trans_id = transaction_instance.transaction_id
                record.save()

                self.message_user(request, f"Dispute(s) successfully resolved.", level=messages.SUCCESS)

    reverse_truncated_debit.short_description = "Reverse Truncated Debit"


class BillsPaymentConstantResourceAdmin(ImportExportModelAdmin):
    search_fields = ["bills_type", "biller"]
    list_filter = (
        ('bills_type', 'bill_provider')
    )
    resource_class = BillsPaymentConstantResource

    def get_list_display(self, request):
        return [field.name for field in self.model._meta.concrete_fields]


class CommissionsRecordResourceAdmin(ImportExportModelAdmin):
    search_fields = ['user__email', 'liberty_reference', 'transaction_id']
    list_filter = (
        ('date_created',)
    )
    resource_class = CommissionsRecordResource
    date_hierarchy = 'date_created'

    def get_list_display(self, request):
        return [field.name for field in self.model._meta.concrete_fields]


class AllBankListResourceAdmin(ImportExportModelAdmin):
    resource_class = AllBankListResource
    search_fields = ['bank_code', 'name', 'cbn_code']

    def get_list_display(self, request):
        return [field.name for field in self.model._meta.concrete_fields]


class GeneralDataDumpResourceAdmin(ImportExportModelAdmin):
    resource_class = GeneralDataDumpResource
    list_filter = (
        ('date_added', 'transaction_type')
    )
    search_fields = ['user__email', 'payload']

    def get_list_display(self, request):
        return [field.name for field in self.model._meta.concrete_fields]


class BillsPaymentDumpDataResourceAdmin(ImportExportModelAdmin):
    autocomplete_fields = ['transaction_instance']
    resource_class = BillsPaymentDumpDataResource
    search_fields = ['user__email', 'liberty_reference', 'transaction_id']
    list_filter = (
        ('transaction_type', 'package_slug', 'bills_type', 'biller', 'is_reversed', 'manaul_update_successful', 'date_added', 'status')
    )
    date_hierarchy = 'date_added'

    def get_list_display(self, request):
        return [field.name for field in self.model._meta.concrete_fields]


class SendMoneyDumpDataResourceAdmin(ImportExportModelAdmin):
    resource_class = SendMoneyDumpDataResource
    search_fields = ['user__email', ]
    list_filter = (
        ('transfer_type', 'money_removed', 'escrow_created', "date_added")
    )

    def get_list_display(self, request):
        return [field.name for field in self.model._meta.concrete_fields]


class TransactionPerformanceResourceAdmin(ImportExportModelAdmin):
    # ordering = ('-date_created', '-send_bank_trans')
    resource_class = TransactionPerformanceResource
    search_fields = ['user__email', ]
    list_filter = (
        ('date_created', 'last_updated', 'user__type_of_user')
    )

    def get_list_display(self, request):
        return [field.name for field in self.model._meta.concrete_fields]


class CashOutChargeBandResourceAdmin(ImportExportModelAdmin):
    resource_class = CashOutChargeBandResource
    list_filter = (
        ('band',)
    )

    def get_list_display(self, request):
        return [field.name for field in self.model._meta.concrete_fields]


class OtherCommissionsRecordResourceAdmin(ImportExportModelAdmin):
    autocomplete_fields = ['agent', 'sales_rep']
    resource_class = OtherCommissionsRecordResource
    search_fields = ['agent__email', 'sales_rep__email', 'liberty_reference', 'transaction_id']
    list_filter = (
        ('transaction_type', 'transaction_owner', 'date_created')
    )

    def get_list_display(self, request):
        return [field.name for field in self.model._meta.concrete_fields]


class UploadSendMoneyDataResourceAdmin(ImportExportModelAdmin):
    resource_class = UploadSendMoneyDataResource
    search_fields = ['request_id', 'title']
    list_filter = (
        ('is_processed', 'date_created')
    )
    date_hierarchy = 'date_created'

    def get_list_display(self, request):
        return [field.name for field in self.model._meta.concrete_fields]


class OutOfBookTransferResourceAdmin(ImportExportModelAdmin):
    def verify_transactions(self, request, queryset):
        if queryset.filter(is_verified=True).exists():
            self.message_user(request, "Error. There is a verified transaction in selected items")
        else:
            # queryset.update(field_name='new_value')
            for query in queryset:
                log_debug("------------------")
                log_info(str(query))
                log_debug("------------------")
                # Verify trans
                verify_trans = VFDBank.vfd_transaction_verification_handler(reference=query.created_liberty_reference)
                query.verification_payload = verify_trans
                query.save()

                if verify_trans["status"] == "00":
                    if verify_trans["data"].get("transactionStatus") == "00":
                        if query.type_of_transfer == "COMMISSION_REVERSAL" and query.is_active == True:
                            complete_comm_reversal = OutOfBookTransfer.complete_reversal_of_commission(query)
                        else:
                            query.is_done = True
                            query.is_verified = True
                            query.save()

            self.message_user(request, "Successfully Checked Transactions")

    verify_transactions.short_description = "Verify Transactions field for selected items"

    resource_class = OutOfBookTransferResource
    search_fields = ['from_account', 'to_account', 'initiate_transaction_payload', 'verification_payload', "created_liberty_reference", "escrow_id"]
    list_filter = (
        ('provider', 'date_created')
    )
    date_hierarchy = 'date_created'
    actions = [verify_transactions]

    def get_list_display(self, request):
        return [field.name for field in self.model._meta.concrete_fields]


class FundingRecordAdmin(ImportExportModelAdmin):
    resource_class = FundingRecordResource
    search_fields = ['record_type']
    list_filter = (
        ('record_type',)
    )

    def get_list_display(self, request):
        return [field.name for field in self.model._meta.concrete_fields]


class DailyFundingRecordAdmin(ImportExportModelAdmin):
    resource_class = DailyFundingRecordResource
    search_fields = ['date']

    def get_list_display(self, request):
        return [field.name for field in self.model._meta.concrete_fields]


class WeekHistoryAdmin(ImportExportModelAdmin):
    resource_class = WeekHistoryResource

    def get_list_display(self, request):
        return [field.name for field in self.model._meta.concrete_fields]


class DailyRewardHistoryAdmin(ImportExportModelAdmin):
    resource_class = DailyRewardHistoryResource

    def get_list_display(self, request):
        return [field.name for field in self.model._meta.concrete_fields]


class MonthlyRewardHistoryAdmin(ImportExportModelAdmin):
    resource_class = MonthlyRewardHistoryResource

    def get_list_display(self, request):
        return [field.name for field in self.model._meta.concrete_fields]


class TransactionRewardAdmin(ImportExportModelAdmin):
    resource_class = TransactionRewardResource

    def get_list_display(self, request):
        return [field.name for field in self.model._meta.concrete_fields]


class LeaderBoardPOSAdmin(ImportExportModelAdmin):
    resource_class = LeaderBoardPOSResource

    def get_list_display(self, request):
        return [field.name for field in self.model._meta.concrete_fields]


class LeaderBoardMobileAdmin(ImportExportModelAdmin):
    resource_class = LeaderBoardMobileResource

    def get_list_display(self, request):
        return [field.name for field in self.model._meta.concrete_fields]


class LeaderBoardAdmin(ImportExportModelAdmin):
    resource_class = LeaderBoardResource

    def get_list_display(self, request):
        return [field.name for field in self.model._meta.concrete_fields]


class POSRequestAdmin(ImportExportModelAdmin):
    resource_class = POSRequestResource
    search_fields = ['date']

    def get_list_display(self, request):
        return [field.name for field in self.model._meta.concrete_fields]


class StartFundByUSSDTranAdmin(ImportExportModelAdmin):
    resource_class = StartFundByUSSDTranResource
    search_fields = ['user__email', 'first_name', 'last_name', 'user_bvn', 'bank_name', 'bank_code', 'liberty_reference', 'redbiller_initiate_data',
                     'redbiller_resolve_data']
    list_filter = (
        ('trans_type', 'trans_complete', 'date_created')
    )
    date_hierarchy = 'date_created'

    def get_list_display(self, request):
        return [field.name for field in self.model._meta.concrete_fields]


class StartFundByUSSDTranAdmin(ImportExportModelAdmin):
    resource_class = StartFundByUSSDTranResource
    search_fields = ['user__email', 'first_name', 'last_name', 'user_bvn', 'bank_name', 'bank_code', 'liberty_reference', 'redbiller_initiate_data',
                     'redbiller_resolve_data', 'coralpay_initiate_data', 'coralpay_resolve_data']
    list_filter = (
        ('trans_type', 'trans_complete', 'date_created', 'status')
    )
    date_hierarchy = 'date_created'

    def get_list_display(self, request):
        return [field.name for field in self.model._meta.concrete_fields]


class DailyUptimeDowntimeRecordAdmin(ImportExportModelAdmin):
    resource_class = DailyUptimeDowntimeRecordResource
    # search_fields = ['user__email', 'first_name', 'last_name', 'user_bvn', 'bank_name', 'bank_code', 'liberty_reference', 'redbiller_initiate_data', 'redbiller_resolve_data']
    list_filter = (
        ('entry_type', 'day_complete', 'date_created')
    )
    date_hierarchy = 'date_created'
    change_list_template = 'user_change_list.html'

    def get_urls(self):
        from django.urls import path

        urls = super().get_urls()
        custom_urls = [
            path(
                'stat_definitions/',
                self.admin_site.admin_view(self.stat_definitions),
                name='stat_definitions',
            )
        ]
        return custom_urls + urls

    def stat_definitions(self, request):
        from django.template.response import TemplateResponse

        context = self.admin_site.each_context(request)

        context["transfers"] = [
            ("00", "Approved or Completed Successfully", "SUCCESSFUL", "No Reversal"),
            ("01", "Status Unknown, Please wait for Settlement Report", "PENDING", "No Reversal"),
            ("02", "Status Unknown, Please wait for Settlement Report", "PENDING", "No Reversal"),
            ("03", "Invalid Sender", "FAILED", "Reversal"),
            ("05", "Do not Honor", "FAILED", "Reversal"),
            ("06", "Dormant Account", "FAILED", "Reversal"),
            ("07", "Invalid Account", "FAILED", "Reversal"),
            ("08", "Account Name Mismatch", "FAILED", "Reversal"),
            ("09", "Request Processing in Progress", "PENDING", "No Reversal"),
            ("12", "Invalid Transaction", "FAILED", "Reversal"),
            ("13", "Invalid Amount", "FAILED", "Reversal"),
            ("14", "Invalid Batch Number", "FAILED", "Reversal"),
            ("15", "Invalid Session or Record ID", "FAILED", "Reversal"),
            ("16", "Unknown Bank Code", "FAILED", "Reversal"),
            ("17", "Invalid Channel", "FAILED", "Reversal"),
            ("18", "Wrong Method Call", "FAILED", "Reversal"),
            ("21", "No Action Taken", "PENDING", "No Reversal"),
            ("25", "Unable to Locate Record", "FAILED", "Reversal"),
            ("26", "Duplicate Record", "PENDING", "No Reversal"),
            ("30", "Format Error", "FAILED", "Reversal"),
            ("34", "Suspected Fraud", "FAILED", "No Reversal"),
            ("35", "Contact Sending Bank", "FAILED", "No Reversal"),
            ("51", "No Sufficient Funds", "FAILED", "No Debit"),
            ("57", "Transaction not Permitted to Sender", "FAILED", "Reversal"),
            ("58", "Transaction not Permitted on Channel", "FAILED", "Reversal"),
            ("61", "Transaction Limit Exceeded", "FAILED", "Reversal"),
            ("63", "Security Violation", "FAILED", "No Reversal"),
            ("65", "Exceeds Withdrawal Frequency", "FAILED", "Reversal"),
            ("68", "Response Received Too Late", "FAILED", "Reversal"),
            ("69", "Unsuccessful Account/Amount Block", "FAILED", "Reversal"),
            ("70", "Unsuccessful Account/Amount Block", "FAILED", "Reversal"),
            ("71", "Empty Mandate Reference Number", "FAILED", "Reversal"),
            ("81", "Transaction Failed", "FAILED", "Reversal"),
            ("91", "Beneficiary Bank Not Available", "FAILED", "Reversal"),
            ("92", "Routing Error", "FAILED", "Reversal"),
            ("94", "Duplicate Transaction", "PENDING", "No Reversal"),
            ("96", "System Malfunction", "PENDING", "No Reversal"),
            ("97", "Timeout Waiting for response from Destination", "FAILED", "Reversal"),
            ("98", "Transaction Exists", "FAILED", "No Debit"),
            ("99", "Transaction Failed", "FAILED", "No Debit"),
            ("500", "Internal server error", "PENDING", "No Reversal"),
            ("null", "Transaction pending", "PENDING", "No Reversal"),
        ]

        return TemplateResponse(
            request,
            'stat_definitions.html',
            context,
        )

    def get_list_display(self, request):
        return [field.name for field in self.model._meta.concrete_fields]


class OtherAppTransNotifyResourceAdmin(ImportExportModelAdmin):
    resource_class = OtherAppTransNotifyResource
    search_fields = ['user__email', 'trans_id']
    list_filter = (
        ('entry', 'trans_type', 'date_added')
    )
    date_hierarchy = 'date_added'

    def get_list_display(self, request):
        return [field.name for field in self.model._meta.concrete_fields]


class OtherServiceAccountSystemResourceAdmin(ImportExportModelAdmin):

    def recreate_accounts(self, request, queryset):

        for query in queryset:
            query: OtherServiceAccountSystem

            main_collection_wallet = WalletSystem.get_wallet(user=query.user, from_wallet_type="COLLECTION")
            if not main_collection_wallet:
                self.message_user(request, message=f"Collection Wallet does not exist, Account Number: {query.account_number}", level=40, )
                continue

            if AccountSystem.objects.filter(account_number=query.account_number).exists():
                self.message_user(request, message=f"Account Already Exists, Account Number: {query.account_number}", level=40, )
                pass
            else:
                AccountSystem.objects.create(
                    user=query.user,
                    wallet=main_collection_wallet,
                    account_provider=query.account_provider,
                    account_type="OTHERS",
                    true_account_type="AJO_USER_PERSONAL" if query.ajo_collector else "PERSONAL",
                    bank_name=query.bank_name,
                    bank_code=query.bank_code,
                    account_number=query.account_number,
                    account_name=query.account_name,
                    is_active=True,
                    available_balance=0.00,
                    is_test=query.is_test,
                    initial_payload=query.initial_payload,
                    payload=query.payload,
                )

                self.message_user(request, f"Successfully Created Accounts, Account Number: {query.account_number}")

    recreate_accounts.short_description = "Recreate Account in Account System"

    autocomplete_fields = ['user', 'requested_by']
    resource_class = OtherServiceAccountSystemResource
    search_fields = ['user__email', 'requested_by__email', 'rc_number', 'initial_payload', 'payload', 'account_number']
    list_filter = (
        ('account_provider', 'account_type', 'vfd_account_type', 'date_created', 'true_account_type', 'bank_name', 'is_active')
    )
    date_hierarchy = 'date_created'
    actions = [recreate_accounts]

    def get_list_display(self, request):
        return [field.name for field in self.model._meta.concrete_fields]


class ParallexTokenResourceAdmin(ImportExportModelAdmin):
    resource_class = ParallexTokenResource

    def get_list_display(self, request):
        return [field.name for field in self.model._meta.concrete_fields]


class QRCodeResourceAdmin(ImportExportModelAdmin):
    resource_class = QRCodeResource

    # exclude = ("qr_code_url",)

    def get_list_display(self, request):
        return [field.name for field in self.model._meta.concrete_fields if field.name not in ["qr_code_url", "verification_payload"]]


class UserOwnAccountResourceAdmin(ImportExportModelAdmin):
    autocomplete_fields = ['user']
    resource_class = UserOwnAccountResource
    search_fields = ['user__email', 'account_number', 'bank_code']
    list_filter = (
        ('date_created',)
    )
    date_hierarchy = 'date_created'

    def get_list_display(self, request):
        return [field.name for field in self.model._meta.concrete_fields]


class RequisitionGroupResourceAdmin(ImportExportModelAdmin):
    autocomplete_fields = ['owner', 'super_admins', 'admins', 'members']
    resource_class = RequisitionGroupResource
    search_fields = ['owner__email', 'super_admins__email', 'admins__email', 'members__email']
    list_filter = (
        ('date_created',)
    )
    date_hierarchy = 'date_created'

    def get_list_display(self, request):
        return [field.name for field in self.model._meta.concrete_fields]


class QRCyberWebhookDataResourceAdmin(ImportExportModelAdmin):
    resource_class = QRCyberWebhookDataResource
    search_fields = ['payload']
    list_filter = (
        ('date_created',)
    )
    date_hierarchy = 'date_created'

    def get_list_display(self, request):
        return [field.name for field in self.model._meta.concrete_fields]


class AutoSweepRecurringChargeTableResourceAdmin(ImportExportModelAdmin):
    autocomplete_fields = ['user', 'wallet']
    resource_class = AutoSweepRecurringChargeTableResource
    search_fields = ['user__email', 'account_number', 'account_name', 'narration']
    list_filter = (
        ('is_active', 'date_created',)
    )
    date_hierarchy = 'date_created'

    def get_list_display(self, request):
        return [field.name for field in self.model._meta.concrete_fields]


class AutoSweepRecurringChargeHistoryResourceAdmin(ImportExportModelAdmin):
    autocomplete_fields = ['recur_instance']
    resource_class = AutoSweepRecurringChargeHistoryResource
    search_fields = ['recur_instance__user__email', 'account_number', 'account_name', 'narration', 'payload', 'escrow_id', 'session_key']
    list_filter = (
        ('status', 'date_created',)
    )
    date_hierarchy = 'date_created'

    def get_list_display(self, request):
        return [field.name for field in self.model._meta.concrete_fields]


class WithdrawalByUSSDNotificationDataResourceAdmin(ImportExportModelAdmin):
    resource_class = WithdrawalByUSSDNotificationDataResource
    search_fields = ['payload']
    list_filter = (
        ('date_created',)
    )
    date_hierarchy = 'date_created'

    def get_list_display(self, request):
        return [field.name for field in self.model._meta.concrete_fields]


class OpeningClosingBalanceResourceAdmin(ImportExportModelAdmin):
    resource_class = OpeningClosingBalanceResource
    list_filter = (
        ('date_created', 'last_updated')
    )
    date_hierarchy = 'date_created'

    def get_list_display(self, request):
        return [field.name for field in self.model._meta.concrete_fields]


class ParallexDumpDataResourceAdmin(ImportExportModelAdmin):
    resource_class = ParallexDumpDataResource

    date_hierarchy = 'date_created'

    def get_list_display(self, request):
        return [field.name for field in self.model._meta.concrete_fields]


class PromoCodeDataResourceAdmin(ImportExportModelAdmin):
    resource_class = PromoCodeDataResource
    date_hierarchy = 'date_created'
    readonly_fields = ['winner_codes']

    def get_list_display(self, request):
        return [field.name for field in self.model._meta.concrete_fields]


class ReconciledWalletDataResourceAdmin(ImportExportModelAdmin):
    autocomplete_fields = ['user']
    resource_class = ReconciledWalletDataResource
    list_filter = (
        ('custom_date', 'is_ok', 'st_wt_bal_match', 'date_created')
    )

    date_hierarchy = 'custom_date'

    def get_list_display(self, request):
        return [field.name for field in self.model._meta.concrete_fields]


class UserDebtResourceAdmin(ImportExportModelAdmin):
    def run_user_debt(self, request, queryset):
        for query in queryset.filter(is_active=True):
            UserDebt.debit_user_debt(trans=query)

        self.message_user(request, "Successfully Checked Commissions")

    run_user_debt.short_description = "Run User Debt"

    autocomplete_fields = ['user', 'wallet']
    resource_class = UserDebtResource
    list_filter = (
        ('date_created', 'is_active')
    )

    date_hierarchy = 'date_created'
    actions = [run_user_debt]

    def get_list_display(self, request):
        return [field.name for field in self.model._meta.concrete_fields]


class PromoCodeDeciderResourceAdmin(ImportExportModelAdmin):

    def get_readonly_fields(self, request, obj=None):
        readonly_fields = list(super().get_readonly_fields(request, obj))

        if obj:
            if obj.start_time not in [None, ''] or obj.end_time not in [None, '']:
                readonly_fields += tuple(['start_time', 'end_time'])

        return readonly_fields

    resource_class = PromoCodeDeciderResource
    list_filter = (
        ('date_created', 'is_active')
    )
    # readonly_fields = ['session_id']
    date_hierarchy = 'date_created'

    def get_list_display(self, request):
        return [field.name for field in self.model._meta.concrete_fields]


class UserAccountNumCountResourceAdmin(ImportExportModelAdmin):
    resource_class = UserAccountNumCountResource
    list_filter = (
        ('date_created',)
    )
    date_hierarchy = 'date_created'

    def get_list_display(self, request):
        return [field.name for field in self.model._meta.concrete_fields]


class LessMinimumAmountCommResourceAdmin(ImportExportModelAdmin):
    autocomplete_fields = ['transf']
    search_fields = ['transf__user__email']
    resource_class = LessMinimumAmountCommResource
    list_filter = (
        ('date_created',)
    )
    date_hierarchy = 'date_created'

    def get_list_display(self, request):
        return [field.name for field in self.model._meta.concrete_fields]


class LienResourceAdmin(ImportExportModelAdmin):
    autocomplete_fields = ['user']
    search_fields = ['user__email', 'description']
    resource_class = LienResource
    list_filter = (
        ('date_created', 'is_active')
    )
    date_hierarchy = 'date_created'

    def get_list_display(self, request):
        return [field.name for field in self.model._meta.concrete_fields]


class PendingTransactionResourceAdmin(ImportExportModelAdmin):
    autocomplete_fields = ['trx_inst', 'trx_verf_obj', 'escrow']
    search_fields = ['user_email', 'escrow__escrow_id', 'trx_inst__transaction_id']
    resource_class = PendingTransactionResource
    list_filter = (
        ('date_created', 'trx_type', 'trx_leg', 'trx_status')
    )
    date_hierarchy = 'date_created'

    def get_list_display(self, request):
        return [field.name for field in self.model._meta.concrete_fields]


class UserPasswordManualSetResourceAdmin(ImportExportModelAdmin):
    autocomplete_fields = ['user', ]
    search_fields = ['user_email', ]
    resource_class = UserPasswordManualSetResource
    list_filter = (
        ('date_created',)
    )
    date_hierarchy = 'date_created'

    def get_list_display(self, request):
        return [field.name for field in self.model._meta.concrete_fields]


class TransactionLimitLogAdmin(ImportExportModelAdmin):
    autocomplete_fields = ['user', ]
    search_fields = ["user_email", ]
    resource_class = TransactionLimitLogResource
    list_filter = (
        ('created_at', )
    )
    list_display = ['user', 'action_type', 'amount', 'account_age_days', 'transaction_id']
    date_hierarchy = 'created_at'


class TerminalPurchaseCommissionResource(resources.ModelResource):
    user_email = fields.Field(column_name='user_email', attribute='user', widget=widgets.ForeignKeyWidget(User, 'email'))
    pos_request_email = fields.Field(column_name='pos_request_email', attribute='pos_request',
                                     widget=widgets.ForeignKeyWidget(POSRequest, 'user__email'))

    class Meta:
        model = TerminalPurchaseCommission
        fields = ('id', 'user_email', 'commission_type', 'settled', 'pos_request_email', 'created_at')


class TerminalPurchaseCommissionAdminModel(ImportExportModelAdmin):
    resource_class = TerminalPurchaseCommissionResource
    autocomplete_fields = ['user', 'pos_request']
    list_display = ['user', 'commission_type', 'settled', 'pos_request', 'created_at']
    list_filter = ['settled', 'commission_type', 'created_at']
    search_fields = ['user__email']

class TransactionDisputeResourceAdmin(ImportExportModelAdmin):
    # autocomplete_fields = ['user_email', 'rrn']
    search_fields = ["customer_account_no", "customer_phone_no", "customer_name", "user_email", "rrn"]
    resource_class = TransactionDisputeResource
    list_filter = (
        ("resolved", "date_created", )
    )
    list_display = ['user_email', 'customer_name', 'customer_phone_no', 'rrn', 'resolved']
    date_hierarchy = 'date_created'


admin.site.register(TransactionDispute, TransactionDisputeResourceAdmin)
admin.site.register(AccountSystem, AccountSystemAdmin)
admin.site.register(AccountCreationFailure, AccountCreationFailureAdmin)
admin.site.register(WalletSystem, WalletSystemResourceAdmin)
admin.site.register(Escrow, EscrowResourceAdmin)
admin.site.register(Transaction, TransactionResourceAdmin)
admin.site.register(Beneficiaries, BeneficiariesResourceAdmin)
admin.site.register(TransferVerificationObject, TransferVerificationObjectResourceAdmin)
admin.site.register(RawPayStack, RawPayStackResourceAdmin)
admin.site.register(PayStackTransaction, PayStackTransactionResourceAdmin)
admin.site.register(CreditCardDetail, CreditCardDetailResourceAdmin)
admin.site.register(WEMACallBack, WEMACallBackAdmin)
admin.site.register(LedgerBalance, LedgerBalanceResourceAdmin)
admin.site.register(LedgerHistoryModel, LedgerHistoryModelResourceAdmin)
admin.site.register(AirtimeToPinParent, AirtimeToPinParentResourceAdmin)
admin.site.register(AirtimeToPinObject, AirtimeToPinObjectResourceAdmin)
admin.site.register(AccountInflowPayload, AccountInflowPayloadResourceAdmin)
admin.site.register(AccountOutflowsTrail, AccountOutflowsTrailResourceAdmin)
admin.site.register(InAppTransactionNotification, InAppTransactionNotificationResourceAdmin)
admin.site.register(DebitCreditRecordOnAccount, DebitCreditRecordOnAccountResourceAdmin)
admin.site.register(BillsPaymentConstant, BillsPaymentConstantResourceAdmin)
admin.site.register(CommissionsRecord, CommissionsRecordResourceAdmin)
admin.site.register(AllBankList, AllBankListResourceAdmin)
admin.site.register(GeneralDataDump, GeneralDataDumpResourceAdmin)
admin.site.register(BillsPaymentDumpData, BillsPaymentDumpDataResourceAdmin)
admin.site.register(SendMoneyDumpData, SendMoneyDumpDataResourceAdmin)
admin.site.register(TransactionPerformance, TransactionPerformanceResourceAdmin)
admin.site.register(MerchantDisbursements, MerchantDisbursementsResourceAdmin)
admin.site.register(CashOutChargeBand, CashOutChargeBandResourceAdmin)
admin.site.register(OtherCommissionsRecord, OtherCommissionsRecordResourceAdmin)
admin.site.register(UploadSendMoneyData, UploadSendMoneyDataResourceAdmin)
admin.site.register(OutOfBookTransfer, OutOfBookTransferResourceAdmin)
admin.site.register(FundingRecord, FundingRecordAdmin)
admin.site.register(DailyFundingRecord, DailyFundingRecordAdmin)
admin.site.register(WeekHistory, WeekHistoryAdmin)
admin.site.register(DailyRewardHistory, DailyRewardHistoryAdmin)
admin.site.register(MonthlyRewardHistory, MonthlyRewardHistoryAdmin)
admin.site.register(TransactionReward, TransactionRewardAdmin)
admin.site.register(LeaderBoardPOS, LeaderBoardPOSAdmin)
admin.site.register(LeaderBoardMobile, LeaderBoardMobileAdmin)
admin.site.register(LeaderBoard, LeaderBoardAdmin)
admin.site.register(POSRequest, POSRequestAdmin)
admin.site.register(StartFundByUSSDTran, StartFundByUSSDTranAdmin)
admin.site.register(DailyUptimeDowntimeRecord, DailyUptimeDowntimeRecordAdmin)
admin.site.register(OtherAppTransNotify, OtherAppTransNotifyResourceAdmin)
admin.site.register(OtherServiceAccountSystem, OtherServiceAccountSystemResourceAdmin)
admin.site.register(ParallexToken, ParallexTokenResourceAdmin)
admin.site.register(DeletedReference, DeletedReferenceResourceAdmin)
admin.site.register(QRCode, QRCodeResourceAdmin)
admin.site.register(UserOwnAccount, UserOwnAccountResourceAdmin)
admin.site.register(RequisitionGroup, RequisitionGroupResourceAdmin)
admin.site.register(QRCyberWebhookData, QRCyberWebhookDataResourceAdmin)
admin.site.register(AutoSweepRecurringChargeTable, AutoSweepRecurringChargeTableResourceAdmin)
admin.site.register(AutoSweepRecurringChargeHistory, AutoSweepRecurringChargeHistoryResourceAdmin)
admin.site.register(WithdrawalByUSSDNotificationData, WithdrawalByUSSDNotificationDataResourceAdmin)
admin.site.register(OpeningClosingBalance, OpeningClosingBalanceResourceAdmin)
admin.site.register(ParallexDumpData, ParallexDumpDataResourceAdmin)
admin.site.register(PromoCodeData, PromoCodeDataResourceAdmin)
admin.site.register(ReconciledWalletData, ReconciledWalletDataResourceAdmin)
admin.site.register(UserDebt, UserDebtResourceAdmin)
admin.site.register(PromoCodeDecider, PromoCodeDeciderResourceAdmin)
admin.site.register(UserAccountNumCount, UserAccountNumCountResourceAdmin)
admin.site.register(LessMinimumAmountComm, LessMinimumAmountCommResourceAdmin)
admin.site.register(Lien, LienResourceAdmin)
admin.site.register(TransactionLimitLog, TransactionLimitLogAdmin)
admin.site.register(PendingTransaction, PendingTransactionResourceAdmin)
admin.site.register(UserPasswordManualSet, UserPasswordManualSetResourceAdmin)
admin.site.register(Region)
admin.site.register(AgencyTeam)
admin.site.register(TerminalPurchaseCommission, TerminalPurchaseCommissionAdminModel)
admin.site.register(SendCommissionScheduler)
