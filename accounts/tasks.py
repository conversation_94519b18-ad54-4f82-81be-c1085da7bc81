import os
import requests
import json
import ast
import pytz
import time
import datetime as task_date_time
import logging

from pprint import pprint
from typing import Optional, Union
from django.db import transaction
from django.db.models import Q, Sum
from django.conf import settings
from django.utils import timezone
from django.core.cache import cache
from django.utils.timezone import now

from django.utils.timezone import now

from celery import shared_task

from accounts.helpers.vfdbank_manager import VFDBank
from accounts.models import (
    CallbackSending, OtherServiceAccountSystem, sending_list, receiving_list,  debit_list, credit_list, Beneficiaries, CommissionsRecord, DebitCreditRecordOnAccount, Escrow, UploadSendMoneyData, WalletSystem, TransferVerificationObject, BillsPaymentDumpData, AccountSystem, Transaction, MerchantDisbursements, MerchantDisbursementManager, TransactionReward, MonthlyRewardHistory, InAppTransactionNotification, WeekHistory, LeaderBoardMobile, DailyRewardHistory, LeaderBoardPOS, AutoSweepRecurringChargeTable, AutoSweepRecurringChargeHistory, PendingTransaction
)
from accounts.helpers.helper_func import notify_admin_on_bills_airtime_low_balance, notify_admin_on_false_credit
from accounts.helpers.general_account_manager import create_temp_trans_access_token, charge_recurring_or_sweep_function
from accounts.helpers.coral_pay_manager import ServicesVASApp
from accounts.models import BillsPaymentConstant
from accounts.helpers.sweep_send_money import send_money

from main.model_choices import AccountProviders, AccountTypes
from main.models import SMSRecord, User, CallbackSystem, ConstantTable
from main.helper.send_emails import send_debit_credit_email, send_email
from main.helper.helper_function import credit_debit_alert_sms_to_user, send_sms_to_user_on_successful_account_creation

from send_money.helpers.helper_functions import fetch_account_name_second_option
from kyc_app.models import BVNDetail
from retail.models import LedgerTableModel
from liberty_pay.celery import celery
from liberty_pay.settings import cloud_messaging
from main.helper.logging_utils import log_info, log_debug, log_warning, log_error, log_critical

from datetime import datetime, timedelta, date

logger = logging.getLogger(__name__)


TIMEZONE = pytz.timezone(settings.TIME_ZONE)

WEEKDAYS = {
    'SUN': 6,
    'MON': 0,
    'TUE': 1,
    'WED': 2,
    'THU': 3,
    'FRI': 4,
    'SAT': 5
}

@shared_task
def merchant_sweep_manager():
    merchants = User.objects.filter(type_of_user="MERCHANT")
    # merchants = User.objects.all()
    if len(merchants) < 1:
        return
    elif len(merchants) > 5:
        try:
            md_manager = MerchantDisbursementManager.objects.get(total_count__gt=0)
            if (md_manager.current_count + 5) >= md_manager.total_count:
                merchants = merchants[md_manager.current_count:]
                md_manager.current_count = md_manager.total_count
                md_manager.save()
            else:
                merchants = merchants[md_manager.current_count:md_manager.current_count + 5]
                md_manager.current_count = md_manager.current_count + 5
                md_manager.save()
        except MerchantDisbursementManager.DoesNotExist:
            try:
                md_manager = MerchantDisbursementManager.objects.get(total_count=0)
            except MerchantDisbursementManager.DoesNotExist:
                md_manager = MerchantDisbursementManager.objects.create()
            md_manager.total_count = len(merchants)
            merchants = merchants[md_manager.current_count:md_manager.current_count + 5]
            md_manager.current_count = md_manager.current_count + 5
            md_manager.save()

    for merchant in merchants:
        dis = MerchantDisbursements.objects.filter(user=merchant)
        if len(dis) > 0:
            for m in dis:
                now = TIMEZONE.localize(datetime.now())
                if m.frequency == 'INSTANT':
                    m.status = "PROCESSING"
                    m.save()
                    send_money(m)
                elif m.frequency == "HOURLY":
                    interval = m.interval
                    if m.previous_runtime:
                        if (now - m.previous_runtime) > timedelta(hours=interval):
                            m.status = "PROCESSING"
                            m.save()
                            send_money(m)
                    else:
                        m.status = "PROCESSING"
                        m.save()
                        send_money(m)
                elif m.frequency == 'DAILY':
                    if m.previous_runtime:
                        if (now - m.previous_runtime) > timedelta(days=1):
                            m.status = "PROCESSING"
                            m.save()
                            send_money(m)
                    else:
                        stated_time = TIMEZONE.localize(datetime.combine(date.today(), m.time))
                        if now >= stated_time:
                            m.status = "PROCESSING"
                            m.save()
                            send_money(m)
                elif m.frequency == 'WEEKLY':
                    if m.previous_runtime:
                        if (now - m.previous_runtime) > timedelta(days=7):
                            m.status = "PROCESSING"
                            m.save()
                            send_money(m)
                    else:
                        this_day = now.weekday()
                        stated_time = TIMEZONE.localize(datetime.combine(date.today(), m.time))
                        if this_day == WEEKDAYS[m.day] and now >= stated_time:
                            m.status = "PROCESSING"
                            m.save()
                            send_money(m)
                elif m.frequency == 'MONTHLY':
                    if m.previous_runtime:
                        if (now - m.previous_runtime) > timedelta(days=30):
                            m.status = "PROCESSING"
                            m.save()
                            send_money(m)
                    else:
                        stated_datetime = TIMEZONE.localize(datetime.combine(m.date, m.time))
                        stated_time = TIMEZONE.localize(datetime.combine(date.today(), m.time))
                        if now.day == stated_datetime.day and now >= stated_time:
                            m.status = "PROCESSING"
                            m.save()
                            send_money(m)

# celery.conf.beat_schedule = {
#     'sweep merchant every 60 seconds': {
#         'task': 'accounts.tasks.merchant_sweep_manager',
#         'schedule': 10,
#     },
# }

# @shared_task
# @shared_task(bind=True, autoretry_for=(Exception,), retry_kwargs={'max_retries': 7, 'countdown': 15})
def create_wallets_and_accounts_task(bvn_instance_id):

    bvn_instance = BVNDetail.objects.filter(id=bvn_instance_id).last()

    user_instance = bvn_instance.kyc.user

    wallet_system = WalletSystem.objects.filter(user=user_instance)
    if wallet_system:
        # Get all Wallet Instances
        get_all_wallets = WalletSystem.get_uncreated_wallets(user=user_instance)

        if "SPEND" not in get_all_wallets:
            spend_wallet = WalletSystem.create_spend_wallet(user=user_instance)

        elif "COLLECTION" not in get_all_wallets:
            collection_wallet = WalletSystem.create_collection_wallet(user=user_instance)

        elif "SAVINGS" not in get_all_wallets:
            savings_wallet = WalletSystem.create_savings_wallet(user=user_instance)

        elif "COMMISSIONS" not in get_all_wallets:
            commissions_wallet = WalletSystem.create_commissions_wallet(user=user_instance)

        # get_spend_wallet = wallet_system.filter(wallet_type="SPEND").first()
        get_collection_wallet = wallet_system.filter(wallet_type="COLLECTION").first()

        AccountSystem.create_accounts(user=user_instance, wallet=get_collection_wallet)

    else:
        WalletSystem.create_wallets(user_id=user_instance.id)

    # Ensure to replace thissssss
    # spend_wallet = WalletSystem.create_spend_wallet(user=user_instance)
    # collection_wallet = WalletSystem.create_collection_wallet(user=user_instance)

    from_provider_type = AccountSystem.get_provider_type(user=user_instance)

    get_account_for_sms = AccountSystem.objects.filter(
        user=user_instance, account_type="COLLECTION",
        account_provider = from_provider_type
    ).last()

    if get_account_for_sms:
        send_sms_to_user_on_successful_account_creation(
            phone_number=user_instance.phone_number,
            bvn_full_name=user_instance.bvn_full_name,
            account_number=get_account_for_sms.account_number,
            bank_name=get_account_for_sms.bank_name
        )
    else:
        pass

    # WalletSystem.create_wallets(user_id=user_id)
    return f"User with user id {user_instance.id} created!"


@shared_task
def send_money_bank_transfer_task(
    escrow_id,
    transaction_id,
    amount,
):
    try:
        escrow_instance = Escrow.objects.get(escrow_id=escrow_id, reversed=False)
    except Exception as e:
        return f"Alreay reversed {e}"

    user = escrow_instance.user

    debit_credit_record = DebitCreditRecordOnAccount.objects.get(id=escrow_instance.debit_credit_record_id, transaction_instance_id__isnull=True)

    debit_credit_record.transaction_instance_id = transaction_id
    debit_credit_record.unique_reference = escrow_id
    debit_credit_record.save()

    external_trans = cache.get(transaction_id)
    if external_trans is None:
        external_trans = Transaction.objects.select_related("escrow_instance").get(transaction_id=transaction_id)
        cache.set(transaction_id, transaction_id)


    send_money_external = AccountSystem.account_send_money_external(
        external_trans=external_trans,
        amount=amount,
    )

    return f"Transfer with escrow_id {escrow_id} sent out for user with id {user.id}"



# @shared_task
# def send_money_external_bank_transfer_task(
#     escrow_id,
#     from_provider_type,
#     provider_fee,
#     unique_reference,
#     ):

#     escrow_instance = Escrow.objects.filter(escrow_id=escrow_id).first()

#     send_external = AccountSystem.account_send_money_external(
#                         user=escrow_instance.user,
#                         from_wallet_type=escrow_instance.from_wallet_type,
#                         from_provider_type=from_provider_type,
#                         provider_fee=provider_fee,
#                         beneficiary_account_name=escrow_instance.to_account_name,
#                         beneficiary_nuban=escrow_instance.to_nuban,
#                         beneficiary_bank_code=escrow_instance.to_bank_code,
#                         narration=escrow_instance.narration,
#                         amount=escrow_instance.amount,
#                         escrow_id=escrow_instance.escrow_id,
#                         unique_reference=unique_reference
#                     )


#     return f"External Transfer with escrow_id {escrow_id} sent out to user with id {escrow_instance.user.id} and wallet type {escrow_instance.from_wallet_type} and provider type {from_provider_type}"

@shared_task
def send_money_external_bank_transfer_task(
    from_wallet_type,
    from_provider_type,
    provider_fee,
    beneficiary_account_name,
    beneficiary_nuban,
    beneficiary_bank_code,
    narration,
    amount,
    escrow_id,
    # unique_reference
    ):

    escrow_instance = Escrow.objects.filter(escrow_id=escrow_id).first()

    send_external = AccountSystem.account_send_money_external(
        user=escrow_instance.user,
        from_wallet_type=from_wallet_type,
        from_provider_type=from_provider_type,
        provider_fee=provider_fee,
        beneficiary_account_name=escrow_instance.to_account_name,
        beneficiary_nuban=escrow_instance.to_nuban,
        beneficiary_bank_code=escrow_instance.to_bank_code,
        narration=escrow_instance.narration,
        amount=escrow_instance.amount,
        escrow_id=escrow_instance.escrow_id,
        # unique_reference=unique_reference
    )


    # return f"External Transfer with escrow_id {escrow_id} sent out to user with id {escrow_instance.user.id} and wallet type {escrow_instance.from_wallet_type} and provider type {from_provider_type}"
    return f"{beneficiary_nuban}"

@shared_task
def send_money_pay_buddy_task(
    sender_wallet_id,
    receiver_wallet_id,
    amount,
    escrow_id,
    customer_reference=None,
    metadata=None
):


    sender_wallet = WalletSystem.objects.get(wallet_id=sender_wallet_id)
    receiver_wallet = WalletSystem.objects.get(wallet_id=receiver_wallet_id)

    sender_transaction = sender_wallet.user.transactions.filter(escrow_id=escrow_id, user=sender_wallet.user).last()
    # if Transaction.objects.filter(escrow_id=escrow_id, user=receiver_wallet.user).exists():

    #     return f"Transfer with escrow_id {escrow_id} already exists"
    # else:

        # sender_user = User.objects.get(id=sender_user_id)
        # buddy_user = User.objects.get(id=buddy_user_id)



    send_money_to_buddy = WalletSystem.fund_wallet_pay_buddy(
        sender_user_instance=sender_wallet.user,
        buddy_user_instance=receiver_wallet.user,
        sender_wallet=sender_wallet,
        receiver_wallet=receiver_wallet,
        amount=amount,
        escrow_id=escrow_id,
        customer_reference=customer_reference,
        metadata=metadata,
        sender_transaction=sender_transaction
    )

    sender_transaction = send_money_to_buddy.get("sender_transaction")
    receiver_transaction = send_money_to_buddy.get("receiver_transaction")

    if sender_transaction:
        sender_transaction.status = "SUCCESSFUL"
        sender_transaction.save()

    log_info("saved")

    if isinstance(receiver_transaction, Transaction):
        receiver_transaction.status = "SUCCESSFUL"
        receiver_transaction.save()

        send_callback_out = send_callback_out_for_send_to_lotto.apply_async(
            queue="processbulksheet",
            kwargs={
                "instance_id": receiver_transaction.id
            }
        )



    # return f"External Transfer with escrow_id {escrow_id} sent out to user with id {escrow_instance.user.id} and wallet type {escrow_instance.from_wallet_type} and provider type {from_provider_type}"
    return "SEND BUDDY DONE"

@shared_task
def pay_commission_to_liberty_task(
    wallet_type,
    from_provider_type,
    source_account_id,
    transaction_commission_id,
    get_escrow_id
    ):

    source_account = AccountSystem.objects.filter(id=source_account_id).last()

    escrow_instance = Escrow.objects.filter(escrow_id=get_escrow_id).last()

    # Pay commission To Liberty
    send_out_commission = WalletSystem.pay_commission_to_liberty(
        user_id = escrow_instance.user.id,
        wallet_id = escrow_instance.from_wallet_id,
        wallet_type = wallet_type,
        liberty_commission = escrow_instance.liberty_commission,
        from_provider_type = from_provider_type,
        get_source_account = source_account,
        transaction_commission_id = transaction_commission_id,
        get_escrow_id = get_escrow_id
    )


    return f"Commission paid for escrow id {get_escrow_id}"


@shared_task
def pay_extra_commission_to_liberty_task(
    user_id,
    wallet_id,
    wallet_type,
    from_provider_type,
    liberty_commission,
    transaction_commission_id,
    transfer_leg,
    transaction_sub_type,
    get_escrow_id,
    ):


    # Pay commission To Liberty

    send_commission = WalletSystem.pay_commission_to_liberty(
        user_id = user_id,
        wallet_id = wallet_id,
        wallet_type=wallet_type,
        liberty_commission = liberty_commission,
        from_provider_type=from_provider_type,
        transaction_commission_id = transaction_commission_id,
        transfer_leg=transfer_leg,
        get_escrow_id = get_escrow_id,
        transaction_sub_type=transaction_sub_type
    )

    return f"Extra Commission paid for escrow id {transaction_commission_id}"



@shared_task
def manage_beneficiaries_task(
    user_id,
    save_beneficiary,
    remove_beneficiary,
    buddy_phone_number=None,
    wallet_id=None,
    account_number=None,
    account_name=None,
    bank_name=None,
    bank_code=None,
    trans_type=None,
):

    user = User.objects.filter(id=user_id).first()

    if buddy_phone_number is not None and account_number is None:
        if trans_type is None:
            manage_ben = Beneficiaries.manage_buddy_beneficiaries(
                user=user,
                save_beneficiary=save_beneficiary,
                remove_beneficiary=remove_beneficiary,
                data={
                    "buddy_phone_number": buddy_phone_number,
                    "wallet_id": wallet_id,
                    "account_name": account_name,
                },
            )
        else:
            manage_ben = Beneficiaries.manage_other_beneficiaries(
                user=user,
                save_beneficiary=save_beneficiary,
                remove_beneficiary=remove_beneficiary,
                data={
                    "buddy_phone_number": buddy_phone_number,
                    "wallet_id": wallet_id,
                    "account_name": account_name,
                },
                trans_type=trans_type
            )

    elif account_number is not None and buddy_phone_number is None:
        manage_ben = Beneficiaries.manage_bank_transfer_beneficiaries(
            user=user,
            save_beneficiary=save_beneficiary,
            remove_beneficiary=remove_beneficiary,
            data={
                "account_number": account_number,
                "account_name": account_name,
                "bank_name": bank_name,
                "bank_code": bank_code,
            },
        )
    return f"beneficiary management for {user}"


@shared_task
def send_out_transaction_payload_task(main_data):
    # send_out = send_transaction_notification_to_horizon_pay(main_data)
    pass

    return f"Notification sent to horizon pay"



# @shared_task
# def task_handle_bills_and_pay_commissions(user_id, amount, biller, bill_provider):
#     get_user = User.objects.get(id=user_id)
#     get_float_user = User.objects.get(email=settings.FLOAT_USER)

#     sharing_result = BillsPaymentConstant.share_commissions_profit(biller, amount, bill_provider)

#     total_profit = sharing_result["total_profit"]
#     liberty_profit = sharing_result["liberty_profit"]
#     agent_cash_profit = sharing_result["agent_cash_profit"]

#     create_record_for_user = CommissionsRecord.create_and_top_up_bill_and_pay_commissions(get_user, amount, total_profit=total_profit, liberty_profit=liberty_profit, agent_cash_profit=agent_cash_profit)

#     create_record_for_float_user = CommissionsRecord.create_and_top_up_bill_and_pay_commissions(get_float_user, amount, total_profit=total_profit, liberty_profit=liberty_profit, agent_cash_profit=agent_cash_profit)

#     return f"Commissions handling done for user - {get_user.email}"



@shared_task
def handle_debit_credit_alert_task(
    phone_number, sms_charge, commissions, amount,
    cr_dr, desc, balance, sms_record_id
):

    sms_record = SMSRecord.objects.filter(id=sms_record_id).last()

    send_cr_dr_sms = credit_debit_alert_sms_to_user(
        phone_number, sms_charge, commissions,
        amount, cr_dr, desc, balance
    )

    if send_cr_dr_sms.get("sent") == True:
        sms_record.is_sent = True
        sms_record.message = send_cr_dr_sms.get("message")
        sms_record.payload = send_cr_dr_sms.get("payload")

    else:
        sms_record.is_sent = False
        sms_record.message = send_cr_dr_sms.get("message")
        sms_record.payload = send_cr_dr_sms.get("payload")

    sms_record.save()

    return f"SMS send for SMS record {sms_record_id}"


@shared_task
def handle_debit_credit_send_email(instance_id):


    instance = Transaction.objects.filter(id=instance_id).last()

    if instance.user.type_of_user != "PERSONAL" or instance.user.email != "<EMAIL>":
        return f"User Not Personal"


    # date_occured = datetime.now().strftime("%d-%m-%Y %H:%M:%S")
    date_occured = instance.date_created.strftime("%d-%m-%Y %H:%M:%S")


    if instance.transaction_type in sending_list:
        send_email_alert = send_debit_credit_email(
            transaction = instance,
            entry_type = "DEBIT",
            date_occured = date_occured
        )

    elif instance.transaction_type in receiving_list:

        send_email_alert = send_debit_credit_email(
            transaction = instance,
            entry_type = "CREDIT",
            date_occured = date_occured
        )

    else:
        send_email_alert = None


    instance.email_sent = True
    instance.email_sent_payload = send_email_alert
    instance.save()


    return f"Email Notification Sent"


# @shared_task
# def send_transaction_to_horizon_pay_task(instance_id):

#     instance = Transaction.objects.filter(id=instance_id).last()

#     if instance.user and instance.status == "SUCCESSFUL" or instance.status == "FAILED":
#         instance.push_notify = True
#         instance.save()

#         main_data = {
#             "status": instance.status,
#             "amount": instance.amount,
#             "commission": instance.liberty_commission,
#             "terminal_id": instance.terminal_id,
#             "timestamp": str(instance.last_updated),
#             "customer_data": {
#                 "customer_id": str(instance.user.customer_id),
#                 "customer_email": instance.user.email,
#             },
#             "transaction": {
#                 "transaction_type": instance.transaction_type,
#                 "transaction_id": str(instance.transaction_id),
#                 "unique_reference": instance.unique_reference
#             }
#         }

#         if instance.transaction_type == "FUND_PAYSTACK":

#             main_data["transaction"]["meta_data"] = {
#                 "provider_status": instance.provider_status
#             }

#             # send_out_transaction_payload_task.delay(
#             #     main_data = main_data,
#             # )

#             send_transaction_notification_to_horizon_pay(main_data)


#         elif instance.transaction_type == "FUND_BANK_TRANSFER":

#             main_data["transaction"]["meta_data"] = {
#                 "source_account_name": instance.source_account_name,
#                 "source_account_number": instance.source_nuban,
#                 "source_bank_code": instance.source_bank_code
#             }

#             # send_out_transaction_payload_task.delay(
#             #     main_data = main_data,
#             # )

#             send_transaction_notification_to_horizon_pay(main_data)

#             instance.push_notify = True
#             instance.push_notify_payload = main_data
#             instance.save()

#         elif instance.transaction_type == "CARD_TRANSACTION_FUND":

#             main_data["transaction"]["meta_data"] = ast.literal_eval(json.loads(json.dumps(instance.card_transactions.last().payload)))

#             # send_out_transaction_payload_task.delay(
#             #     main_data=main_data
#             # )

#             send_transaction_notification_to_horizon_pay(main_data)

#             # instance.push_notify = True
#             # instance.push_notify_payload = main_data
#             # instance.save()


#         elif instance.transaction_type == "SEND_BANK_TRANSFER" and instance.transaction_leg == "EXTERNAL":

#             main_data["transaction"]["meta_data"] =  {
#                 "beneficiary_account_name": instance.beneficiary_account_name,
#                 "beneficiary_account_number": instance.beneficiary_nuban,
#                 "beneficiary_bank_code": instance.beneficiary_bank_code
#             }

#             # send_out_transaction_payload_task.delay(
#             #     main_data = main_data
#             # )

#             send_transaction_notification_to_horizon_pay(main_data)

#             # instance.push_notify = True
#             # instance.push_notify_payload = main_data
#             # instance.save()
#     else:
#         pass




@shared_task
def check_for_false_credit_task(instance_id, previous_bal, current_bal, discrepancy):

    instance = WalletSystem.objects.filter(id=instance_id).last()

    time.sleep(3)

    one_minute_ago = timezone.now() - timezone.timedelta(minutes=1)

    credit_record = DebitCreditRecordOnAccount.objects.filter(user=instance.user, entry="CREDIT", amount=discrepancy, date_created__gte=one_minute_ago).last()

    if not credit_record:
        notify_admin_on_false_credit(
            user_email = instance.user.email,
            false_amount = discrepancy,
            user_balance_before = previous_bal,
            user_balance_after = current_bal,
        )

    else:
        # credit_record = DebitCreditRecordOnAccount.objects.filter(id=credit_record_id).last()

        get_transaction = Transaction.objects.filter(transaction_id=credit_record.transaction_instance_id).last()
        if not get_transaction or discrepancy != credit_record.amount:

            notify_admin_on_false_credit(
                user_email = instance.user.email,
                false_amount = discrepancy,
                user_balance_before = previous_bal,
                user_balance_after = current_bal,
            )


        else:
            pass





# @shared_task
def process_bank_name_for_upload_send_money_data(request_id, transfer_type):

    instances = UploadSendMoneyData.objects.filter(request_id=request_id)

    if transfer_type == "BUDDY":

        for instance in instances:
            buddy_phone_number = instance.buddy_phone_number
            get_user = User.objects.filter(phone_number=buddy_phone_number).last()
            instance.is_processed = True

            if get_user and WalletSystem.get_wallet(user=get_user, from_wallet_type="COLLECTION"):
                account_name = get_user.bvn_full_name
                instance.is_good = True
                instance.account_name = account_name


                is_beneficiary = False
                save_beneficiary = False
                remove_beneficiary = False
                is_recurring = False
                narration = instance.narration
                amount = float(instance.amount)


                send_money_data = {
                    "buddy_phone_number": buddy_phone_number,
                    "amount": amount,
                    "narration": narration,
                    "is_beneficiary": is_beneficiary,
                    "save_beneficiary": save_beneficiary,
                    "remove_beneficiary": remove_beneficiary,
                    "is_recurring": is_recurring,
                    "customer_reference": instance.unique_reference,
                }

                instance.send_money_data = send_money_data
            else:
                instance.account_name = None

            instance.save()


    else:
        for instance in instances:
            account_number = instance.account_number
            bank_code = instance.bank_code
            instance.is_processed = True

            get_account_name = fetch_account_name_second_option(
                account_number=account_number,
                bank_code=bank_code
            )

            if get_account_name["status"] != True:
                instance.account_name = None
            else:
                instance.is_good = True
                account_name = get_account_name["data"]["account_name"]
                instance.account_name = account_name

                account_name = account_name
                narration = instance.narration
                is_beneficiary = False
                save_beneficiary = False
                remove_beneficiary = False
                ledger_commission = 0.00
                is_recurring = False
                commission_type = None
                amount = float(instance.amount)


                send_money_data = {
                    "account_number": account_number,
                    "account_name": account_name,
                    "bank_code": bank_code,
                    "bank_name": instance.bank_name,
                    "amount": amount,
                    "narration": narration,
                    "is_beneficiary": is_beneficiary,
                    "save_beneficiary": save_beneficiary,
                    "remove_beneficiary": remove_beneficiary,
                    "is_recurring": is_recurring,
                    "ledger_commission": ledger_commission,
                    "commission_type": commission_type,
                    "customer_reference": instance.unique_reference,
                }



                instance.send_money_data = send_money_data

            instance.save()




# @shared_task
# def process_float_bal_before_and_after(instance_id):

    # instance = DebitCreditRecordOnAccount.objects.filter(id=instance_id).last()

#     print("I AM HERERRRRRRR FOR DEBIT CREDIT")
#     print("I AM HERERRRRRRR FOR DEBIT CREDIT")
#     print("I AM HERERRRRRRR FOR DEBIT CREDIT")
#     print(instance.amount)
#     print(instance.amount)
#     print(instance.amount)
#     print("I AM HERERRRRRRR FOR DEBIT CREDIT")
#     print("I AM HERERRRRRRR FOR DEBIT CREDIT")
#     print("I AM HERERRRRRRR FOR DEBIT CREDIT")
#     print("I AM HERERRRRRRR FOR DEBIT CREDIT")
#     if instance.transaction_instance_id:
#         float_user = WalletSystem.get_float_user()

#         if instance.user != float_user:

#             transaction_instance = Transaction.objects.filter(transaction_id=instance.transaction_instance_id).last()

#             if transaction_instance:
#                 print("I AM HAVE A TRANSACTION")
#                 print("I AM HAVE A TRANSACTION")
#                 print("I AM HAVE A TRANSACTION")
#                 print("I AM HAVE A TRANSACTION")
#                 print(transaction_instance.id)

#                 get_previous = Transaction.objects.filter(date_created__lt=transaction_instance.date_created).filter().order_by('-id')[0]
#                 print(get_previous.id)


#                 if get_previous:
#                     if get_previous.float_bal_after is None or get_previous.float_bal_after == 0:
#                         float_bal_before = list(WalletSystem.objects.exclude(user=float_user).filter(wallet_type="COLLECTION").aggregate(Sum('available_balance')).values())[0]
#                     else:
#                         float_bal_before = get_previous.float_bal_after
#                 else:
#                     float_bal_before = list(WalletSystem.objects.exclude(user=float_user).filter(wallet_type="COLLECTION").aggregate(Sum('available_balance')).values())[0]

#                 print(float_bal_before)
#                 print(float_bal_before)
#                 print(float_bal_before)

#                 instance.float_bal_before = float_bal_before
#                 transaction_instance.float_bal_before = float_bal_before

#                 if instance.entry == "CREDIT":
#                     instance.float_bal_after = transaction_instance.float_bal_before + instance.amount
#                     transaction_instance.float_bal_after = transaction_instance.float_bal_before + instance.amount
#                 else:
#                     instance.float_bal_after = transaction_instance.float_bal_before - instance.amount
#                     transaction_instance.float_bal_after = transaction_instance.float_bal_before - instance.amount

#                 # instance.save()
#                 print(transaction_instance.float_bal_before)
#                 print(transaction_instance.float_bal_before)
#                 print(transaction_instance.float_bal_after)
#                 print(transaction_instance.float_bal_after)
#                 transaction_instance.save()

#                 if transaction_instance.escrow_id is not None:
#                     get_all_escrow_transactions = Transaction.objects.exclude(transaction_id=transaction_instance.transaction_id).filter(escrow_id=transaction_instance.escrow_id)
#                     if get_all_escrow_transactions:
#                         for trans in get_all_escrow_transactions:
#                             trans.float_bal_before = transaction_instance.float_bal_before
#                             trans.float_bal_after = transaction_instance.float_bal_after
#                             trans.save()
#             else:
#                 print("I DONT HAVE A TRANSACTION")
#                 print("I DONT HAVE A TRANSACTION")
#                 print("I DONT HAVE A TRANSACTION")
#                 pass

#         else:
#             print("I AM A FLOAT USER IN DEBIT CREDIT")
#             print("I AM A FLOAT USER IN DEBIT CREDIT")
#             print("I AM A FLOAT USER IN DEBIT CREDIT")
#             print("I AM A FLOAT USER IN DEBIT CREDIT")
#             pass

#     else:
#         print("I DONT HAVE A TRNASCTION TIED TO MY DEBIT CREDIT")
#         print("I DONT HAVE A TRNASCTION TIED TO MY DEBIT CREDIT")
#         print("I DONT HAVE A TRNASCTION TIED TO MY DEBIT CREDIT")
#         print("I DONT HAVE A TRNASCTION TIED TO MY DEBIT CREDIT")
#         pass




@shared_task
def get_balance_on_every_transfer_task(account_number, account_provider):

    if settings.ENVIRONMENT == "development":
        pass
    else:
        accounts = AccountSystem.objects.filter(account_provider=account_provider, account_number=account_number)
        for account in accounts:
            if account_provider == "VFD":
                response = VFDBank.vfd_account_enquiry(account_number=account_number)
                if response["message"] == 'Account Not Found':
                    pass
                else:
                    account.available_balance = float(response["data"]["accountBalance"]) if response.get("data", {}).get("accountBalance") is not None else 0.00
                    account.save()

    return True



@shared_task
def create_verification_trans_task(url, payload, headers):

    try:
        response = requests.request("POST", url, data=payload, headers=headers, timeout=60)
        resp = response.json()
    except requests.exceptions.RequestException as e:
        logger.error(e)
        resp = {
            "status": "error",
            "message": f"{e}"
        }
    log_info("HERE IS THE RESPONSE FROM THE VERIFICATION TASK...!!!!")
    log_info("HERE IS THE RESPONSE FROM THE VERIFICATION TASK...!!!!")
    log_info("HERE IS THE RESPONSE FROM THE VERIFICATION TASK...!!!!")
    log_info("HERE IS THE RESPONSE FROM THE VERIFICATION TASK...!!!!")
    log_info(str(resp))

    # session = requests.Session()
    # try:
    #     response = session.post(url, data=payload, headers=headers, timeout=60)
    #     resp = response.json()
    # except requests.exceptions.RequestException as e:
    #     logger.error(e)
    #     resp = {
    #         "status": "error",
    #         "message": f"{e}"
    #     }

    # session.close()


    return resp



@shared_task
def notify_admin_on_bills_airtime_low_balance_task(service_name, message=None):

    for phone_number in ["2347039516293", "2348077469471", "2348167631246", "2348031346306"]:
        notify_admin_on_bills_airtime_low_balance(phone_number, service_name, message)

    return True




#### shared tasks for Rewards ####

@shared_task
def daily_rewards():
    today_date = datetime.now()
    yesterday = today_date - timedelta(days = 1)

    # instance = TransactionReward.objects.filter(transaction_date__gte=today_date).distinct()
    # instance = TransactionReward.objects.distinct("user").all().filter(Q(transaction_date__year=yesterday_year, transaction_date__month=yesterday_month))
    instance = TransactionReward.objects.distinct("user").all().filter(transaction_date__date=yesterday)

    user_history_data = []
    for i in instance:
        user_id = i.user_id
        transaction_coin = TransactionReward.objects.filter(Q(transaction_date__date=yesterday, user=user_id)).aggregate(Sum('coin_amount'))
        coin = transaction_coin["coin_amount__sum"] or 0
        eligible_transaction_coin = TransactionReward.objects.filter(Q(transaction_date__date=yesterday, user=user_id) & Q(transaction_type__in=["SEND_MONEY", "WITHDRAWAL"])).aggregate(Sum('coin_amount'))
        eligible_coin = eligible_transaction_coin["coin_amount__sum"] or 0
        send_money_count = TransactionReward.objects.filter(Q(transaction_type="SEND_MONEY", user=user_id, transaction_date__date=yesterday)).count()
        withdrawal_count = TransactionReward.objects.filter(Q(transaction_type="WITHDRAWAL", user=user_id, transaction_date__date=yesterday)).count()
        airtime_data_count = TransactionReward.objects.filter(Q(transaction_type="AIRTIME_DATA", user=user_id, transaction_date__date=yesterday)).count()
        def cash_back_history_amount():
            if int(send_money_count + withdrawal_count + airtime_data_count) >= int(settings.DAILY_REWARD_TARGET_COIN):
                transaction_percentage_amount = TransactionReward.objects.filter(Q(transaction_date__date =yesterday, user=user_id)).aggregate(Sum('transaction_percentage_amount'))
                cash_back_amount = transaction_percentage_amount["transaction_percentage_amount__sum"] or 0
                amount = '{0:.2f}'.format(cash_back_amount)
            else:
                amount = '{0:.2f}'.format(0)
            return amount
        def eligible_cash_back_history_amount():
            if int(send_money_count + withdrawal_count) >= int(settings.DAILY_REWARD_TARGET_COIN):
                transaction_percentage_amount = TransactionReward.objects.filter(Q(transaction_date__date =yesterday, user=user_id)).aggregate(Sum('transaction_percentage_amount'))
                cash_back_amount = transaction_percentage_amount["transaction_percentage_amount__sum"] or 0
                amount = '{0:.2f}'.format(cash_back_amount)
            else:
                amount = '{0:.2f}'.format(0)
            return amount
        cash_back_reward = cash_back_history_amount()
        eligible_cash_back_reward = eligible_cash_back_history_amount()

        transaction_count = int(send_money_count + withdrawal_count + airtime_data_count)
        eligible_transaction_count = int(send_money_count + withdrawal_count)
        history_data = {
            "user_id": user_id,
            "send_money_count":send_money_count,
            "withdrawal_count":withdrawal_count,
            "airtime_data_count":airtime_data_count,
            "transaction_count": transaction_count,
            "eligible_transaction_count": eligible_transaction_count,
            "coin":coin,
            "eligible_coin":eligible_coin,
            "cash_back_amount": cash_back_reward,
            "eligible_cash_back_amount": eligible_cash_back_reward,
            "date": yesterday
        }
        log_info(yesterday, "YESTERDAY")
        DailyRewardHistory.objects.create(
            user = i.user,
            send_money_count = send_money_count,
            withdrawal_count = withdrawal_count,
            airtime_data_count = airtime_data_count,
            transaction_count = transaction_count,
            eligible_transaction_count = eligible_transaction_count,
            coin = coin,
            eligible_coin = eligible_coin,
            cash_back_amount = cash_back_reward,
            eligible_cash_back_amount = eligible_cash_back_reward,
            transaction_date = yesterday
        )
        user_history_data.append(history_data)
        log_info(str(user_history_data))
    return user_history_data

@shared_task
def monthly_rewards():
    today_date = datetime.now()
    yesterday = today_date - timedelta(days = 1)

    log_info(str(yesterday))
    # instance = TransactionReward.objects.filter(transaction_date__gte=today_date).distinct()
    # instance = TransactionReward.objects.distinct("user").all().filter(Q(transaction_date__year=yesterday_year, transaction_date__month=yesterday_month))
    instance = TransactionReward.objects.distinct("user").all().filter(transaction_date__date=yesterday)
    log_info(str(instance))

    user_history_data = []
    for i in instance:
        user_id = i.user_id
        log_info(str(i.user_id))
        transaction_coin = TransactionReward.objects.filter(Q(transaction_date__date=yesterday, user=user_id)).aggregate(Sum('coin_amount'))
        coin = transaction_coin["coin_amount__sum"] or 0
        eligible_transaction_coin = TransactionReward.objects.filter(Q(transaction_date__date=yesterday, user=user_id) & Q(transaction_type__in=["SEND_MONEY", "WITHDRAWAL"])).aggregate(Sum('coin_amount'))
        eligible_coin = eligible_transaction_coin["coin_amount__sum"] or 0
        send_money_count = TransactionReward.objects.filter(Q(transaction_type="SEND_MONEY", user=user_id, transaction_date__date=yesterday)).count()
        withdrawal_count = TransactionReward.objects.filter(Q(transaction_type="WITHDRAWAL", user=user_id, transaction_date__date=yesterday)).count()
        airtime_data_count = TransactionReward.objects.filter(Q(transaction_type="AIRTIME_DATA", user=user_id, transaction_date__date=yesterday)).count()
        def cash_back_history_amount():
            if int(send_money_count + withdrawal_count + airtime_data_count) >= int(settings.DAILY_REWARD_TARGET_COIN):
                transaction_percentage_amount = TransactionReward.objects.filter(Q(transaction_date__date =yesterday, user=user_id)).aggregate(Sum('transaction_percentage_amount'))
                cash_back_amount = transaction_percentage_amount["transaction_percentage_amount__sum"] or 0
                amount = '{0:.2f}'.format(cash_back_amount)
            else:
                amount = '{0:.2f}'.format(0)
            return amount
        def eligible_cash_back_history_amount():
            if int(send_money_count + withdrawal_count) >= int(settings.DAILY_REWARD_TARGET_COIN):
                transaction_percentage_amount = TransactionReward.objects.filter(Q(transaction_date__date =yesterday, user=user_id)).aggregate(Sum('transaction_percentage_amount'))
                cash_back_amount = transaction_percentage_amount["transaction_percentage_amount__sum"] or 0
                amount = '{0:.2f}'.format(cash_back_amount)
            else:
                amount = '{0:.2f}'.format(0)
            return amount
        cash_back_reward = cash_back_history_amount()
        eligible_cash_back_reward = eligible_cash_back_history_amount()


        transaction_count = int(send_money_count + withdrawal_count + airtime_data_count)
        eligible_transaction_count = int(send_money_count + withdrawal_count)
        history_data = {
            "user_id": user_id,
            "send_money_count":send_money_count,
            "withdrawal_count":withdrawal_count,
            "airtime_data_count":airtime_data_count,
            "transaction_count": transaction_count,
            "eligible_transaction_count": eligible_transaction_count,
            "coin":coin,
            "eligible_coin":eligible_coin,
            "cash_back_amount": cash_back_reward,
            "eligible_cash_back_amount": eligible_cash_back_reward,
            "date": yesterday
        }
        log_info(yesterday, "YESTERDAY")
        MonthlyRewardHistory.objects.create(
            user = i.user,
            send_money_count = send_money_count,
            withdrawal_count = withdrawal_count,
            airtime_data_count = airtime_data_count,
            transaction_count = transaction_count,
            eligible_transaction_count = eligible_transaction_count,
            coin = coin,
            eligible_coin = eligible_coin,
            cash_back_amount = cash_back_reward,
            eligible_cash_back_amount = eligible_cash_back_reward,
            transaction_date = yesterday
        )
        user_history_data.append(history_data)
        log_info(str(user_history_data))
    return user_history_data

@shared_task
def leader_board_pos():
    today_month = datetime.now().month
    today_year = datetime.now().year
    instance = TransactionReward.objects.distinct("user").all().filter(device_type="MOBILE", transaction_date__year=today_year, transaction_date__month=today_month).values('user','user__first_name', 'user__last_name', 'user__phone_number')
    log_info(str(instance))
    user_history_data = []
    for i in instance:
        user_id = i["user"]
        user_name = f'{i["user__first_name"]} {i["user__first_name"]}'
        phone_number = f'{i["user__phone_number"][:4]}*****{i["user__phone_number"][-3:]}'
        log_info(i["user"])
        transaction_coin = TransactionReward.objects.filter(Q(device_type="MOBILE", transaction_date__year=today_year, transaction_date__month=today_month, user=user_id)).aggregate(Sum('coin_amount'))
        coin = transaction_coin["coin_amount__sum"] or 0
        send_money_count = TransactionReward.objects.filter(Q(transaction_type="SEND_MONEY", device_type="MOBILE", user=user_id, transaction_date__year=today_year, transaction_date__month=today_month)).count()
        withdrawal_count = TransactionReward.objects.filter(Q(transaction_type="WITHDRAWAL", device_type="MOBILE", user=user_id, transaction_date__year=today_year, transaction_date__month=today_month)).count()
        airtime_data_count = TransactionReward.objects.filter(Q(transaction_type="AIRTIME_DATA", device_type="MOBILE", user=user_id, transaction_date__year=today_year, transaction_date__month=today_month)).count()

        transaction_count = int(send_money_count + withdrawal_count + airtime_data_count)
        eligible_transaction_count = int(send_money_count + withdrawal_count)
        history_data = {
            "user_id": User.objects.get(id=user_id).email,
            "send_money_count":send_money_count,
            "withdrawal_count":withdrawal_count,
            "user_name": user_name,
            "phone_number":phone_number,
            "airtime_data_count":airtime_data_count,
            "transaction_count": transaction_count,
            "eligible_transaction_count": eligible_transaction_count,
            "coin":coin,
            "date": datetime.now()
            # "date": f'{today_month}:{today_year}'

        }
        user_history_data.append(history_data)

    ### rank user function
    def rankFunc(e):
        return e['eligible_transaction_count']
    user_history_data.sort(key=rankFunc, reverse=True)
    # print(user_history_data)
    user_rank = []
    for i in range(0, len(user_history_data)):
        user_history_data[i].update({"rank": i+1})
        user_rank.append(user_history_data[i])

    data = {
        "all_ranks": user_history_data,
    }
    LeaderBoardPOS.objects.create(
        month = today_month,
        year = today_year,
        user_rank = data
    )
    return data

@shared_task
def leader_board_pos():
    today_date = datetime.now().date()
    first = today_date.replace(day=1)
    last_month = first - timedelta(days=1)
    today_month = last_month.month
    today_year = last_month.year
    instance = TransactionReward.objects.distinct("user").all().filter(device_type="POS", transaction_date__year=today_year, transaction_date__month=today_month).values('user','user__first_name', 'user__last_name', 'user__phone_number')
    log_info(str(instance))
    user_history_data = []
    for i in instance:
        user_id = i["user"]
        user_name = f'{i["user__first_name"]} {i["user__first_name"]}'
        phone_number = f'{i["user__phone_number"][:4]}*****{i["user__phone_number"][-3:]}'
        log_info(i["user"])
        transaction_coin = TransactionReward.objects.filter(Q(device_type="POS", transaction_date__year=today_year, transaction_date__month=today_month, user=user_id)).aggregate(Sum('coin_amount'))
        coin = transaction_coin["coin_amount__sum"] or 0
        cash_back = TransactionReward.objects.filter(Q(device_type="POS", transaction_type__in=["SEND_MONEY","WITHDRAWAL"], transaction_date__year=today_year, transaction_date__month=today_month, user=user_id)).aggregate(Sum('transaction_percentage_amount'))
        coin_back_amount = cash_back["transaction_percentage_amount__sum"] or 0
        send_money_count = TransactionReward.objects.filter(Q(transaction_type="SEND_MONEY", device_type="POS", user=user_id, transaction_date__year=today_year, transaction_date__month=today_month)).count()
        withdrawal_count = TransactionReward.objects.filter(Q(transaction_type="WITHDRAWAL", device_type="POS", user=user_id, transaction_date__year=today_year, transaction_date__month=today_month)).count()
        airtime_data_count = TransactionReward.objects.filter(Q(transaction_type="AIRTIME_DATA", device_type="POS", user=user_id, transaction_date__year=today_year, transaction_date__month=today_month)).count()

        transaction_count = int(send_money_count + withdrawal_count + airtime_data_count)
        eligible_transaction_count = int(send_money_count + withdrawal_count)
        history_data = {
            "user_id": User.objects.get(id=user_id).email,
            "send_money_count":send_money_count,
            "withdrawal_count":withdrawal_count,
            "airtime_data_count":airtime_data_count,
            "transaction_count": transaction_count,
            "eligible_transaction_count": eligible_transaction_count,
            "cash_back_amount": coin_back_amount,
            "user_name": user_name,
            "phone_number":phone_number,
            "coin":coin,
            "date": datetime.now()
            # "date": f'{today_month}:{today_year}'

        }
        user_history_data.append(history_data)

    ### rank user function
    def rankFunk(e):
        return e['coin']
    user_history_data.sort(key=rankFunk, reverse=True)
    def rankFunc(e):
        return e['eligible_transaction_count']
    user_history_data.sort(key=rankFunc, reverse=True)
    # print(user_history_data)
    user_rank = []
    for i in range(0, len(user_history_data)):
        user_history_data[i].update({"rank": i+1})
        user_rank.append(user_history_data[i])

    data = {
        "all_ranks": user_history_data,
    }
    LeaderBoardPOS.objects.create(
        month = today_month,
        year = today_year,
        user_rank = data
    )
    return data

@shared_task
def leader_board_mobile():

    today_date = datetime.now().date()
    first = today_date.replace(day=1)
    last_month = first - timedelta(days=1)
    today_month = last_month.month
    today_year = last_month.year
    instance = TransactionReward.objects.distinct("user").all().filter(device_type="MOBILE", transaction_date__year=today_year, transaction_date__month=today_month).values('user','user__first_name', 'user__last_name', 'user__phone_number')
    log_info(str(instance))
    user_history_data = []
    for i in instance:
        user_id = i["user"]
        user_name = f'{i["user__first_name"]} {i["user__first_name"]}'
        phone_number = f'{i["user__phone_number"][:4]}*****{i["user__phone_number"][-3:]}'
        log_info(i["user"])
        transaction_coin = TransactionReward.objects.filter(Q(device_type="MOBILE", transaction_date__year=today_year, transaction_date__month=today_month, user=user_id)).aggregate(Sum('coin_amount'))
        coin = transaction_coin["coin_amount__sum"] or 0
        cash_back = TransactionReward.objects.filter(Q(device_type="MOBILE", transaction_type__in=["SEND_MONEY","WITHDRAWAL"], transaction_date__year=today_year, transaction_date__month=today_month, user=user_id)).aggregate(Sum('transaction_percentage_amount'))
        cash_back_amount = cash_back["transaction_percentage_amount__sum"] or 0
        send_money_count = TransactionReward.objects.filter(Q(transaction_type="SEND_MONEY", device_type="MOBILE", user=user_id, transaction_date__year=today_year, transaction_date__month=today_month)).count()
        withdrawal_count = TransactionReward.objects.filter(Q(transaction_type="WITHDRAWAL", device_type="MOBILE", user=user_id, transaction_date__year=today_year, transaction_date__month=today_month)).count()
        airtime_data_count = TransactionReward.objects.filter(Q(transaction_type="AIRTIME_DATA", device_type="MOBILE", user=user_id, transaction_date__year=today_year, transaction_date__month=today_month)).count()

        transaction_count = int(send_money_count + withdrawal_count + airtime_data_count)
        eligible_transaction_count = int(send_money_count + withdrawal_count)
        history_data = {
            "user_id": User.objects.get(id=user_id).email,
            "send_money_count":send_money_count,
            "withdrawal_count":withdrawal_count,
            "cash_back_amount": cash_back_amount,
            "airtime_data_count":airtime_data_count,
            "transaction_count": transaction_count,
            "eligible_transaction_count": eligible_transaction_count,
            "user_name": user_name,
            "phone_number":phone_number,
            "coin":coin,
            "date": datetime.now()
            # "date": f'{today_month}:{today_year}'

        }
        user_history_data.append(history_data)

    ### rank user function
    def rankFunk(e):
        return e['coin']
    user_history_data.sort(key=rankFunk, reverse=True)
    def rankFunc(e):
        return e['eligible_transaction_count']
    user_history_data.sort(key=rankFunc, reverse=True)
    # print(user_history_data)
    user_rank = []
    for i in range(0, len(user_history_data)):
        user_history_data[i].update({"rank": i+1})
        user_rank.append(user_history_data[i])

    data = {
        "all_ranks": user_history_data,
    }
    LeaderBoardMobile.objects.create(
        month = today_month,
        year = today_year,
        user_rank = data
    )
    return data

@shared_task
def get_transaction_data():
    #### send money transaction
    float_user = WalletSystem.get_float_user()
    lte_date = datetime.now().date() - timedelta(days=1)
    gte_date = datetime.now().date()
    get_previous_reward = TransactionReward.objects.all().values_list("transaction_id", flat=True)
    base_transactions = Transaction.objects.exclude(user=float_user, transaction_id__in=list(get_previous_reward)).exclude(date_created__lte=lte_date, date_created__gte=gte_date).order_by("-id")
    #base_transactions = Transaction.objects.exclude(user=float_user, date_created__lte="2023-01-12")
    send_money_transaction_type_list = ["SEND_BANK_TRANSFER", "SEND_BUDDY"]
    send_money_trns_qs = base_transactions.filter(Q(transaction_type__in=send_money_transaction_type_list, transaction_leg__in=["COMMISSIONS", "INTERNAL"], status="SUCCESSFUL", liberty_commission__isnull=False))[:100]
    # print( "SEND_MONEY::::::::::", send_money_trns_qs, "\n")
    for send_money in send_money_trns_qs:
        # print(send_money.liberty_commission)
        transaction_exist = TransactionReward.objects.filter(transaction_id=send_money.transaction_id).first()
        if transaction_exist:
            pass
        else:
            TransactionReward.objects.create(
                transaction_id=send_money.transaction_id,
                user = send_money.user,
                coin_amount = 1,
                amount = send_money.liberty_commission,
                transaction_type = "SEND_MONEY",
                transaction_date = send_money.date_created,
                device_type = send_money.user_wallet_type
            )

    ##### airtime_data
    #bills_trns_amount = BillsPaymentDumpData.objects.exclude(user=float_user, date_added__lte="2023-01-12").filter(transaction_instance__liberty_commission__isnull=False).filter(Q(status="SUCCESSFUL") & Q(biller__endswith="DATA") | Q(biller__endswith="VTU"))
    bills_trns_amount = BillsPaymentDumpData.objects.exclude(user=float_user, transaction_id__in=list(get_previous_reward), date_added__lte=lte_date).filter(transaction_instance__liberty_commission__isnull=False).filter(Q(status="SUCCESSFUL") & Q(biller__endswith="DATA") | Q(biller__endswith="VTU")).order_by("-id")[:100]
    # print("AIRTIME_DATA::::::::::", bills_trns_amount, "\n")
    for airtime_data in bills_trns_amount:
        # print(airtime_data.transaction_instance.liberty_commission)
        transaction_exist = TransactionReward.objects.filter(transaction_id=airtime_data.transaction_instance.transaction_id).first()

        if transaction_exist:
            pass
        else:
            TransactionReward.objects.create(
                transaction_id=airtime_data.transaction_instance.transaction_id,
                user = airtime_data.user,
                coin_amount = 1,
                amount = airtime_data.transaction_instance.liberty_commission,
                transaction_type = "AIRTIME_DATA",
                transaction_date = airtime_data.transaction_instance.date_created,
                device_type = airtime_data.transaction_instance.user_wallet_type
            )


    #### withdrawal
    cashout_transaction_type_list = ["CARD_TRANSACTION_FUND_TRANSFER", "CARD_TRANSACTION_FUND"]
    pos_withdrawals = base_transactions.filter(Q(transaction_type__in=cashout_transaction_type_list, status="SUCCESSFUL", liberty_commission__isnull=False))[:100]
    # print("WITHDRAWAL::::::::::", pos_withdrawals, "\n")
    for withdrawal in pos_withdrawals:
        # print(withdrawal.liberty_commission)
        transaction_exist = TransactionReward.objects.filter(transaction_id=withdrawal.transaction_id).first()
        if transaction_exist:
            pass
        else:
            TransactionReward.objects.create(
                transaction_id=withdrawal.transaction_id,
                user = withdrawal.user,
                coin_amount = 1,
                amount = withdrawal.liberty_commission,
                transaction_type = "SEND_MONEY",
                transaction_date = withdrawal.date_created,
                device_type = withdrawal.user_wallet_type
            )

    return f"successful\n SEND_MONEY:::::::::: {send_money_trns_qs}\n AIRTIME_DATA:::::::::: {bills_trns_amount}\n WITHDRAWAL:::::::::: {pos_withdrawals}"


@shared_task
def weekly_history():
    a_week = WeekHistory.objects.last()
    start_date = a_week.week_end_date + timedelta(days=1)
    end_date = a_week.week_end_date + timedelta(days=7)
    log_info(str(start_date, end_date))
    WeekHistory.objects.create(week_start_date=start_date, week_end_date=end_date)
    return f"start date is {start_date}, end data is {end_date}"
#### end of shared tasks for rewards



@shared_task
def handle_send_callback_task(instance_id):


    instance = Transaction.objects.filter(id=instance_id).last()

    send_callback = CallbackSystem.send_callback(
        user = instance.user,
        transaction_type = instance.transaction_type,
        payload = instance.callback_payload,
        transaction_instance = instance
    )


    return 'Done'



@shared_task
def send_callback_out_for_other_account_fund(instance_id, account_number, trans_owner_user_id):

    log_info("came hereee")


    instance = Transaction.objects.filter(id=instance_id).last()
    get_account_inst_owner = User.objects.filter(id=trans_owner_user_id).last()

    # payload = {
    #     "user_id": instance.lotto_agent_user_id,
    #     "reference": instance.liberty_reference,
    #     "amount": instance.amount,
    #     "liberty_commission": instance.liberty_commission,
    #     "agent_phone": instance.lotto_agent_user_phone,
    #     "account_number": account_number,
    #     # "response": instance.payload
    # }

    payload = eval(instance.callback_payload)

    send_callback = CallbackSystem.send_callback(
        user = get_account_inst_owner,
        transaction_type = instance.transaction_type,
        payload = payload,
        transaction_instance = instance
    )


    return 'Done'



@shared_task
def send_callback_out_for_send_to_lotto(instance_id, trx=None):
    instance = Transaction.objects.get(id=instance_id) if trx is None else trx
    if instance:

        if instance.callback_payload == None or  instance.callback_payload == ' ' or instance.callback_payload == '':
            Transaction.serializer_trans(instance)

        check_callback = CallbackSystem.objects.filter(
            user = instance.user,
            transaction_type = instance.transaction_type
        )



        if not check_callback:
            return "NO_CALLBACK"

        send_callback_task = CallbackSystem.send_callback(
            user = instance.user,
            transaction_type = instance.transaction_type,
            payload = instance.callback_payload,
            transaction_instance = instance
        )

        return send_callback_task

    return 'Done'


@shared_task
def send_callback_out_for_send_to_ajo(instance_id):
    instance = Transaction.objects.filter(id=instance_id).last()
    if instance:

        if instance.callback_payload == None or  instance.callback_payload == ' ' or instance.callback_payload == '':
            Transaction.serializer_trans(instance)

        check_callback = CallbackSystem.objects.filter(
            user = instance.user,
            transaction_type = instance.transaction_type
        )

        if check_callback:

            send_callback_task = CallbackSystem.send_callback(
                user = instance.user,
                transaction_type = instance.transaction_type,
                payload = instance.callback_payload,
                transaction_instance = instance
            )

            log_info(str(send_callback_task))


    return 'Done'



# @shared_task
# def set_user_band_based_on_previous_day():




@shared_task
def auto_debit_user(auto_charge_table_id):
    auto_charge_table_instance = AutoSweepRecurringChargeTable.objects.filter(id=auto_charge_table_id).last()

    if auto_charge_table_instance:
        # obtain the information needed
        user = auto_charge_table_instance.user
        min_bal = auto_charge_table_instance.min_balance
        max_amount = auto_charge_table_instance.max_amount
        user_wallet = auto_charge_table_instance.wallet
        recur_type = auto_charge_table_instance.recur_type

        amount_to_charge = max_amount
        check_transaction_charge = ConstantTable.calculate_send_money_transaction_charge(user=user, amount=amount_to_charge)
        amount_to_charge_with_comm = amount_to_charge + check_transaction_charge


        if recur_type == "RECUR" and (user_wallet.available_balance - amount_to_charge_with_comm) <= min_bal:
            amount_to_charge = amount_to_charge_with_comm - min_bal

        if recur_type == "SWEEP":
            initial_amount_to_charge = user_wallet.available_balance - min_bal
            second_amount = initial_amount_to_charge - ConstantTable.calculate_send_money_transaction_charge(user=user, amount=initial_amount_to_charge)

            if second_amount > 0:
                amount_to_charge = second_amount



        log_info(str(amount_to_charge))




        # Create RecurTransaction and Generate Session ID
        # Generate a unique session key

        epoch = int(time.time())
        expiration_minutes = 5
        expiration_time = epoch + (expiration_minutes * 60)

        session_key = f"{str(epoch)[-10:]}-{expiration_time}"

        # Create Access Token

        access_token = create_temp_trans_access_token(session_key=session_key, temp_user_id=user.id, added_time=150)

        create_recur_trans = AutoSweepRecurringChargeHistory.objects.create(
            recur_instance = auto_charge_table_instance,
            amount = amount_to_charge,
            # customer_reference = Transaction.create_liberty_reference(suffix="ASWP_TRNS"),
            account_number = auto_charge_table_instance.account_number,
            account_name = auto_charge_table_instance.account_name,
            bank_name = auto_charge_table_instance.bank_name,
            bank_code = auto_charge_table_instance.bank_code,
            narration = auto_charge_table_instance.narration,
            session_key = session_key
        )


        charge_user_transfer = charge_recurring_or_sweep_function(
            data=create_recur_trans,
            access_token=access_token,
            trans_key=auto_charge_table_instance.trans_key
        )

        create_recur_trans.payload = json.dumps(charge_user_transfer)
        if charge_user_transfer["status"] == True:
            if charge_user_transfer.get("data").get("error") == None:
                create_recur_trans.escrow_id = charge_user_transfer.get("data").get("data", {"escrow_id": None}).get("escrow_id")

        create_recur_trans.save()

        return {
            "status": True,
            "message": "All Done",
        }



# @shared_task
def set_next_charge_time():
    now = timezone.now()
    current_hour = now.hour

    start_time = now - timezone.timedelta(minutes=2)
    end_time = now + timezone.timedelta(minutes=2)

    instances = AutoSweepRecurringChargeTable.objects.filter(
        next_run_time__hour=current_hour, is_active=True
    )

    # next_run_time__time__range=[start_time, end_time], is_active=True
    log_info(str(now))
    log_info(str(current_hour))
    # print(start_time)
    # print(end_time)

    # start_time = task_date_time.time(start_time.hour, start_time.minute, start_time.second)
    # end_time = task_date_time.time(end_time.hour, end_time.minute, end_time.second)
    log_info(str(instances))

    date_now = datetime.now()

    for instance in instances:
        log_info(str(instance.next_run_time.hour))

        if instance.start_date.replace(tzinfo=None) <= now.replace(tzinfo=None):
            new_now = timezone.make_aware(date_now, timezone=timezone.get_default_timezone())

            if instance.sweep_interval == "HOURLY":
                next_run_time = new_now + timedelta(hours=1)
                next_run_time2 = datetime.combine(next_run_time, datetime.min.time()) + timedelta(hours=next_run_time.hour)

            elif instance.sweep_interval == "DAILY":
                next_run_time = new_now + timedelta(days=1)
                next_run_time2 = datetime.combine(next_run_time, datetime.min.time()) + timedelta(hours=instance.sweep_hour)

            elif instance.sweep_interval == "WEEKLY":
                next_run_time = new_now + timedelta(days=7)
                next_run_time2 = datetime.combine(next_run_time, datetime.min.time()) + timedelta(hours=instance.sweep_hour)

            elif instance.sweep_interval == "MONTHLY":
                next_run_time = new_now.replace(month=new_now.month + 1)

                # Handle the case where the current month is December
                if next_run_time.month == 1:
                    next_run_time = next_run_time.replace(year=next_run_time.year + 1)

                next_run_time2 = datetime.combine(next_run_time, datetime.min.time()) + timedelta(hours=instance.sweep_hour)

            else:
                break

            instance.last_run_time = now
            instance.next_run_time = next_run_time2

            instance.save()

            # call the task to debit the user
            auto_debit_user(auto_charge_table_id=instance.id)

            return "DONE"
        else:
            return "N0T UP TO START DATE"


def realtime_handle_ledger_input_task(instance_id, charge_type, charge=None, type_of_task="LEDGER_INPUT", recon_data={}):

    try:
        if type_of_task == "LEDGER_INPUT":
            if isinstance(instance_id, int):
                instance = Transaction.objects.filter(id=instance_id).first()
            else:
                instance = Transaction.objects.filter(transaction_id=instance_id).first()


            if instance and not LedgerTableModel.objects.filter(transaction=instance).exists():
                # if instance.transaction_type in credit_list or (instance.status in accepted_status_list and instance.transaction_type in debit_list):
                if instance.transaction_type in credit_list+debit_list:
                    if instance.transaction_type == "SEND_BANK_TRANSFER" and instance.transaction_leg == "EXTERNAL":
                        pass
                    else:
                        get_escrow = Escrow.objects.filter(escrow_id=instance.escrow_id).first()
                        if get_escrow and instance.transaction_type not in credit_list:
                            charge = get_escrow.pos_charge
                            charge_type = get_escrow.pos_charge_type

                        if instance.transaction_type in ["CARD_TRANSACTION_FUND", "CARD_TRANSACTION_FUND_TRANSFER"]:
                            charge, charge_type = LedgerTableModel.get_charge_type(transaction_instance=instance)


                        if instance.transaction_type == "FUND_BANK_TRANSFER" and instance.user.type_of_user == "LIBERTY_RETAIL":
                            get_source_account = AccountSystem.objects.filter(user=instance.user, account_number=instance.beneficiary_nuban, true_account_type="CORPORATE_LIBERTY_RETAIL").last()

                            if get_source_account:

                                LedgerTableModel.handle_transfer_in(transaction_instance=instance)
                            else:
                                LedgerTableModel.create_new_ledger_object(transaction_instance=instance, charge_type=charge_type, charge=charge, recon_data=recon_data)
                        else:
                            LedgerTableModel.create_new_ledger_object(transaction_instance=instance, charge_type=charge_type, charge=charge, recon_data=recon_data)

        elif type_of_task == "SMS_CHARGE":
            instance = DebitCreditRecordOnAccount.objects.filter(id=instance_id).first()
            if instance:
                LedgerTableModel.create_sms_ledger_object(debcred_instance=instance, recon_data=recon_data)
        else:
            pass



    except Exception as err:
        LedgerTableModel.objects.create(
            error_id = f"{type_of_task}/{instance_id}",
            error_msg = f"{err}"
        )

    return "Done"


@shared_task
def handle_ledger_input_task(instance_id, charge_type, charge=None, type_of_task="LEDGER_INPUT"):

    realtime_handle_ledger_input_task(instance_id, charge_type, charge=charge, type_of_task=type_of_task, recon_data={})

    return


def main_handle_interval_recon(escrow_id, verf_id):
    re_failed_trans = TransferVerificationObject.objects.filter(escrow_id=escrow_id, transaction_type="RE_FAILED_TRANSFER_IN").last()

    new_verf_id = int(verf_id)
    transaction_verf = TransferVerificationObject.objects.get(id=new_verf_id)


    if re_failed_trans or not transaction_verf or transaction_verf.transaction_leg != "INTERNAL":
        pass

    else:

        liberty_reference = transaction_verf.liberty_reference
        new_liberty_reference = Transaction.create_liberty_reference_with_old_reference(liberty_reference=transaction_verf.liberty_reference, suffix="RVSL")
        amount = transaction_verf.amount
        transfer_charge = 0
        liberty_commission = 0
        narration = "RE_FAILED_TRANSFER_IN"
        user = transaction_verf.transaction_instance.user




        get_ben_account = AccountSystem.get_float_account(from_wallet_type="FLOAT", from_provider_type=transaction_verf.transaction_instance.account_provider)

        failed_trans_exists = Transaction.objects.filter(escrow_id=escrow_id, transaction_type="RE_FAILED_TRANSFER_IN").exists()

        verify_trans = VFDBank.vfd_transaction_verification_handler(reference=liberty_reference)

        if isinstance(verify_trans, dict):

            transaction_verf.checked_for_duplicate = True

            if verify_trans["status"] == "00" and get_ben_account and not failed_trans_exists and verify_trans["data"].get("transactionStatus") == "00" and amount == float(verify_trans["data"].get("amount")):

                try:
                    verf_timestamp = eval(verify_trans).get("data", {}).get("transactionDate")
                except TypeError:
                    verf_timestamp = verify_trans.get("data", {}).get("transactionDate")
                except:
                    verf_timestamp = None

                transaction_verf.raw_payload_response = json.dumps(verify_trans)
                transaction_verf.unique_reference = f'{verify_trans.get("data").get("bankTransactionId")}'
                transaction_verf.has_duplicate = True
                transaction_verf.verf_timestamp = verf_timestamp
                transaction_verf.trans_status_code = verify_trans["data"].get("transactionStatus")

                transaction_verf.transaction_instance.unique_reference = f'{verify_trans.get("data").get("bankTransactionId")}'


                transaction_verf.save()
                transaction_verf.transaction_instance.save()


                if get_ben_account and not failed_trans_exists:

                    re_internal_transaction = Transaction.objects.create(
                        user=user,
                        account_provider=transaction_verf.account_provider,
                        transaction_type="RE_FAILED_TRANSFER_IN",
                        amount=transaction_verf.amount,
                        provider_fee=transfer_charge,
                        liberty_reference=new_liberty_reference,
                        liberty_commission=liberty_commission,
                        extra_fee=0,
                        user_trans_band = user.trans_band,
                        escrow_id=transaction_verf.escrow_id,
                        beneficiary_account_name=get_ben_account.account_name,
                        beneficiary_nuban=get_ben_account.account_number,
                        beneficiary_bank_code=get_ben_account.bank_code,
                        source_nuban=transaction_verf.transaction_instance.beneficiary_nuban,
                        narration=narration,
                        status="IN_PROGRESS",
                        transaction_leg="RE_INTERNAL",
                    )

                    log_info("created transaction")

                    handle_rev = AccountSystem.handle_out_of_books(
                        from_account=transaction_verf.transaction_instance.beneficiary_nuban,
                        to_account=get_ben_account.account_number,
                        amount=amount,
                        escrow_id=escrow_id,
                        user_bvn_number=user.bvn_number,
                        transaction_instance=re_internal_transaction,
                        liberty_reference=new_liberty_reference,
                    )

                    log_info("handled_reversal")

            else:
                transaction_verf.save()


                # if settings.ENVIRONMENT == "development":
                #     all_numbers = ["0","1","2","3","4","5","6","7","8","9"]
                #     transbank_id = ''.join(random.choice(all_numbers) for i in range(7))

                #     response = {
                #         "status":"SUCCESSFUL",
                #         "account_provider": transaction_verf.account_provider,
                #         "trans_bank_id": transbank_id,
                #         "user_email": user.email,
                #         "amount": amount,
                #         "escrow_id": escrow_id,
                #         "liberty_reference": liberty_reference,
                #         "transaction_leg": "INTERNAL",
                #         "transaction_type": "SEND_BANK_TRANSFER",
                #         "transaction_status_code": "00",
                #         "payload":{
                #             "status":"00",
                #             "message":"Successful Transaction Retrieval",
                #             "data":{
                #                 "TxnId": liberty_reference,
                #                 "amount": amount,
                #                 "accountNo": "**********",
                #                 "fromAccountNo":"**********",
                #                 "transactionStatus":"00",
                #                 "transactionDate":"2022-10-06 14:01:24.0",
                #                 "toBank":"999999",
                #                 "fromBank":"999999",
                #                 "sessionId":"",
                #                 "bankTransactionId": transbank_id,
                #                 "transactionType":"OUTFLOW"
                #             }
                #         }
                #     }


                #     push_out_new_legs_from_verf(response_data=response)



@shared_task
def handle_interval_reconciliation(escrow_id, verf_id):

    main_handle_interval_recon(escrow_id=escrow_id, verf_id=verf_id)



@shared_task
def handle_sms_notification_task(user_id, amount, cr_dr, narration, from_wallet_type, liberty_commission, transaction_instance_id):
    from accounts.models import UnsentSMS
    from main.helper.helper_function import credit_debit_alert_sms_to_user, convert_num_to_currency
    from time import sleep

    user = User.objects.get(id=user_id)
    whisper_charge = WalletSystem.get_sms_charge(user, "SMS_CHARGE", amount)
    sms_unsent_raw_data = {}

    # WalletSystem.transaction_alert_notfication_manager(
    #     user=sender_user_instance,
    #     amount=float(amount),
    #     cr_dr=cr_dr,
    #     narration=narration,
    #     from_wallet_type=from_wallet_type,
    #     transaction_instance_id=transaction_instance_id
    # )

    if transaction_instance_id is None:
        unsent_sms = UnsentSMS.objects.create(
            user = user,
            wallet_type = from_wallet_type,
            charge = whisper_charge,
            fail_reason = "trans instance id is None",
            sms_unsent_raw_data = sms_unsent_raw_data,
        )

        return False

    if user.sms_subscription:
        wallets_queryset = WalletSystem.fetch_wallets(user=user)
        wallet = wallets_queryset.filter(wallet_type=from_wallet_type).first()

        # Fund Float Commission wallet
        whisper_user = User.objects.filter(email=settings.FLOAT_USER_EMAIL).first()

        if liberty_commission is not None:
            commissions = liberty_commission
        else:
            commissions = 0.00

        new_amount = amount - commissions

        # Check if balance is enough for sms

        if wallet.available_balance < whisper_charge:
            sms_unsent_raw_data = dict(
                phone_number = user.phone_number,
                sms_charge = convert_num_to_currency(whisper_charge),
                commissions = convert_num_to_currency(liberty_commission) if liberty_commission else 0,
                amount = convert_num_to_currency(new_amount),
                cr_dr = cr_dr,
                desc = narration,
                balance = convert_num_to_currency(wallet.available_balance)
            )

            unsent_sms = UnsentSMS.objects.create(
                user = user,
                wallet_type = from_wallet_type,
                charge = whisper_charge,
                fail_reason = "LOW_BALANCE",
                sms_unsent_raw_data = sms_unsent_raw_data,
            )

        else:
            # Remove whisper charge
            WalletSystem.deduct_balance(user=user, wallet=wallet, amount=whisper_charge, trans_type="WHISPER_CHARGE", transaction_instance_id=transaction_instance_id)

            # Fund Whisper Account

            get_whisper_wallet = WalletSystem.get_float_wallet(from_wallet_type="WHISPER")

            WalletSystem.fund_balance(
                user=whisper_user,
                wallet=get_whisper_wallet,
                amount=whisper_charge,
                trans_type="WHISPER_CHARGE",
                transaction_instance_id=transaction_instance_id
            )

            # from_provider_type = AccountSystem.get_provider_type(user=user)
            # account_instance = AccountSystem.get_account_type(user, wallet.wallet_type, from_provider_type)


            sms_record = SMSRecord.objects.create(
                user = user,
                receiver = user.phone_number
            )


            send_cr_dr_sms = credit_debit_alert_sms_to_user(
                phone_number = user.phone_number,
                sms_charge = convert_num_to_currency(whisper_charge),
                commissions = convert_num_to_currency(commissions),
                amount = convert_num_to_currency(new_amount),
                cr_dr = cr_dr,
                desc = narration,
                balance = convert_num_to_currency(wallet.available_balance)
            )

            if send_cr_dr_sms.get("sent") == True:
                sms_record.is_sent = True

            else:
                sms_record.is_sent = False

            sms_record.message = send_cr_dr_sms.get("message")
            sms_record.payload = send_cr_dr_sms.get("payload")
            sms_record.response_payload = send_cr_dr_sms.get("whisper_resp")

            sms_record.save()


            # print(send_cr_dr_sms)

            # if send_cr_dr_sms.get("sent") == True:
            #     sms_record.is_sent = True
            #     sms_record.message = send_cr_dr_sms.get("message")
            #     sms_record.payload = send_cr_dr_sms.get("payload")

            # else:
            #     sms_record.is_sent = False
            #     sms_record.message = send_cr_dr_sms.get("message")
            #     sms_record.payload = send_cr_dr_sms.get("payload")

            # sms_record.save()

    else:
        pass


    return


@shared_task
def handle_cloud_notification_send_task(title, body, data, token):
    from firebase_admin import messaging

    try:
        message = messaging.Message(
            notification=messaging.Notification(
                title=title,
                body=body,
            ),
            data=data,
            token=token,
        )


        # Send a message to the device corresponding to the provided
        # registration token.

        response = messaging.send(message)
        # Response is a message ID string.
    except Exception as e:
        pass


    return




@shared_task
def handle_bills_payment_task(all_data):
    # Check that transaction has not been reversed


    main_data = all_data["main_data"]
    vas_id = all_data["vas_id"]
    trans_id = all_data["trans_id"]
    wallet_id = all_data["wallet_id"]
    biller = all_data["biller"]
    bill_provider = all_data["bill_provider"]
    ussd_extra_comm = all_data["ussd_extra_comm"]

    vas_dump_data = BillsPaymentDumpData.objects.get(id=vas_id)
    create_transaction = Transaction.objects.get(id=trans_id)
    user_instance = create_transaction.user
    wallet_instance = WalletSystem.objects.get(id=wallet_id)
    amount = create_transaction.amount
    liberty_commission = create_transaction.liberty_commission
    escrow_id = create_transaction.escrow_id

    local_channel = create_transaction.transaction_mode

    if not vas_dump_data.sent and create_transaction.status not in ["FAILED", "SUCCESSFUL"] and vas_dump_data.status not in ["FAILED", "SUCCESSFUL"]:

        vas_dump_data.sent = True
        vas_dump_data.save()

        if settings.ENVIRONMENT == "development":
            send_to_coral_pay_endpoint= {'error': False, 'status': 'success', 'message': 'Successfully processed the vend request', 'responseCode': '00', 'responseData': {'name': 'GLO VTU', 'paymentReference': create_transaction.liberty_reference, 'transactionId': 'BP16600457189048341925_2333', 'vendStatus': 'CONFIRMED', 'narration': 'Successfully proces for the customer', 'statusCode': '00', 'amount': amount, 'convenienceFee': 0.0, 'customerMessage': "Your GLO VTU payment of NGN 100.00 for 07039516293 was successful. Thank you for using the service"}}
        else:
            send_to_coral_pay_endpoint = ServicesVASApp.send_coralpay_data_to_endpoint(main_data=main_data)


        vas_dump_data.external_service_data = str(send_to_coral_pay_endpoint)
        vas_dump_data.token = str(send_to_coral_pay_endpoint)
        vas_dump_data.reference = str(send_to_coral_pay_endpoint)
        vas_dump_data.save()


        if isinstance(send_to_coral_pay_endpoint, dict):

            if send_to_coral_pay_endpoint.get("error") == False:
                unique_reference = send_to_coral_pay_endpoint.get("responseData").get("transactionId")
                provider_status = send_to_coral_pay_endpoint.get("responseData").get("vendStatus")



                formatted_customer_message = send_to_coral_pay_endpoint.get("responseData").get("customerMessage")

                if provider_status != "CONFIRMED":
                    create_transaction.status = "PENDING"
                    create_transaction.save()

                    response = {
                        "error": False,
                        "success": False,
                        "message": "Transaction Processing"
                    }
                else:
                    create_transaction.status = "SUCCESSFUL"

                    create_transaction.unique_reference = unique_reference
                    create_transaction.provider_status = provider_status
                    create_transaction.vas_customer_message = formatted_customer_message
                    create_transaction.save()

                    vas_dump_data.status = "SUCCESSFUL"
                    vas_dump_data.save()

    ##########################################################################################################################################
                    # SEND OUT APP NOTIFICATION
                    not_token=user_instance.firebase_key
                    not_title="Transaction Successful"
                    not_body=f"You have successfully performed a BILLS and PAYMENT transaction of N{amount}. COMM - {liberty_commission}"
                    not_data={"amount_sent": f"{amount}", "available_balance": f"{wallet_instance.available_balance}"}


                    send_out_notification = cloud_messaging.send_broadcast(
                        token=not_token,
                        title=not_title,
                        body=not_body,
                        data=not_data
                    )

                    InAppTransactionNotification.create_in_app_transaction_notification(
                        user=user_instance,
                        title=not_title,
                        message_body=not_body
                    )

    #####################################################################################################

                    # HANDLE COMMISSIONS AND PROFITS

                    get_profits = BillsPaymentConstant.share_commissions_profit(biller, amount, bill_provider)

                    total_profit = get_profits["total_profit"] + ussd_extra_comm
                    liberty_profit = get_profits["liberty_profit"] if local_channel != "USSD" else total_profit
                    agent_cash_profit = get_profits["agent_cash_profit"] if local_channel != "USSD" else 0
                    provider_fee = get_profits["provider_fee"]


                    log_info(str(total_profit))
                    log_info(str(liberty_profit))
                    log_info(str(agent_cash_profit))
                    log_info(str(provider_fee))

                    # Fund User commission wallet
                    CommissionsRecord.create_and_top_up_bill_and_pay_commissions(
                        user = user_instance,
                        amount = amount,
                        biller = biller,
                        transaction_id = create_transaction.transaction_id,
                        total_profit = total_profit,
                        liberty_profit = liberty_profit,
                        agent_cash_profit = agent_cash_profit,
                        provider_fee = provider_fee,
                        escrow_id = escrow_id,
                    )



    ##########################################################################################################################


                response = {
                    "error": False,
                    "success": True,
                    "message": formatted_customer_message
                }

            elif send_to_coral_pay_endpoint.get("error") == True or send_to_coral_pay_endpoint.get("status") == "failed":

                response_code = send_to_coral_pay_endpoint.get("responseCode")
                # provider_status = send_to_coral_pay_endpoint.get("responseData").get("vendStatus")
                provider_status = send_to_coral_pay_endpoint.get("status")

                # Refund Money
                reversal_transaction = Transaction.reverse_bills_airpin_transactions(transaction=create_transaction, provider_status=provider_status)


                vas_dump_data.status = "FAILED"
                vas_dump_data.save()

                if response_code == "51":
                    notify_admin_on_bills_airtime_low_balance_task.delay(service_name="CORAL PAY", message=send_to_coral_pay_endpoint.get("message"))

                response = {
                    "error": True,
                    "success": False,
                    "message": "Transaction Failed. Please Try Again"
                }

            else:
                response = {
                    "error": False,
                    "success": False,
                    "message": "Transaction Processing"
                }
        else:
            response = {
                "error": False,
                "success": False,
                "message": "Transaction Processing"
            }





@shared_task
def resolve_parallex_dump():
    from accounts.helpers.helper_func import one_way_decrypt_trans_notify
    from accounts.helpers.parallex_manager import ParallexBank
    from accounts.models import ParallexDumpData

    for ref in ParallexDumpData.objects.filter(trans_ref__isnull=False, tsq_data__isnull=True):

        result = one_way_decrypt_trans_notify(ref.trans_ref)

        details = ParallexBank.notify_webhook_get_trans_det(trans_id=result)

        ref.tsq_data = json.dumps(details)
        ref.save()

    return True


@shared_task
def get_all_acct_balances(include_accts: list=None):
    import pandas as pd

    accounts_needed = AccountSystem.objects.filter(account_provider="VFD", account_type__in=["COLLECTION", "OTHERS"]).distinct("account_number")

    if include_accts:
        include_list = include_accts.split(',')
        accounts_needed = accounts_needed.filter(account_number__in=include_list)

    log_info(f"Total accounts: {accounts_needed.count()}")

    # Counter variable to keep track of loop iteration
    table_needed = []
    iteration_count = 0

    for account in accounts_needed:
        iteration_count += 1

        log_info(iteration_count,"We are at")

        vfd_balance = VFDBank.get_vfd_float_balance(account_number=account.account_number)

        if vfd_balance is not None and vfd_balance > 0:

            data = {
                "user_email": account.user.email,
                "account_name": account.account_name,
                "account_number": account.account_number,
                "available_balance": vfd_balance,
            }

            table_needed.append(data)

        else:
            pass

    df = pd.DataFrame(table_needed)
    df.to_csv("all_acct_bals.csv")

    return "Done"



@shared_task
def process_account_transactions(acct_num_list):
    import pandas as pd
    import numpy as np

    if not acct_num_list or not isinstance(acct_num_list, list):
        return

    float_account_num = AccountSystem.get_dynamic_float_account(from_wallet_type="FLOAT", from_provider_type="VFD")


    for account_number in acct_num_list:

        from_date = date(2022, 10, 1).strftime("%Y-%m-%d")
        to_date = date.today().strftime("%Y-%m-%d")
        page = 1


        data_list = []
        logger.info("::::::::::::::::::::::::::::::", data_list)

        while True:
            data = VFDBank.get_account_transactions(page, from_date, to_date, account_number)
            page += 1

            if not data:
                break

            main_data = data.get("data")

            if not main_data:
                break

            log_info(str(main_data))


            data_list.extend(main_data)

            if any(item.get("runningBalance", 0) == 0 for item in main_data):
                break



        if data_list:
            df = pd.DataFrame(data_list)
            # df['other_id'] = df['paymentDetailData'].apply(lambda x: x['id'])
            df['date'] = pd.to_datetime(df['date'].apply(lambda x: f"{x[0]}-{x[1]:02d}-{x[2]:02d}"))
            df['transactionTime'] = df['transactionTime'].apply(lambda x: f"{x['hour']:02d}:{x['minute']:02d}:{x['second']:02d}")
            df['combined_datetime'] = df['date'].dt.strftime('%Y-%m-%d') + ' ' + df['transactionTime']
            df['from_account'] = df['paymentDetailData'].apply(lambda x: x['accountNumber'])
            df['bank_number'] = df['paymentDetailData'].apply(lambda x: x['bankNumber'])
            df['session_id'] = df['paymentDetailData'].apply(lambda x: x['receiptNumber'])
            df['note'] = df['paymentDetailData'].apply(lambda x: x['remarks'])
            # df['trans_type'] = np.where(df['from_account'] == "**********", "internal",
                                            # np.where(df['from_account'] == f"{account_number}", "external",
                                                        # np.where(df['note'].str.startswith("CHRG"), "charge", "inflow")))
            def get_category(payment_detail_data):
                try:
                    return payment_detail_data['category']
                except KeyError:
                    return None

            def get_trans_type(payment_detail_data):
                from_account = payment_detail_data['accountNumber']
                try:
                    return "internal" if from_account == float_account_num else "external" if from_account == account_number else "charge" if payment_detail_data['remarks'].startswith("CHRG") else None

                except KeyError:
                    return None

            df['category'] = df['paymentDetailData'].apply(get_category)
            df['trans_type'] = df['paymentDetailData'].apply(get_trans_type)
            df = df[['id', 'accountId', 'accountNo', 'category', 'from_account', 'amount', 'runningBalance', 'trans_type', 'combined_datetime', 'note', 'bank_number', 'session_id']]
            df.to_csv(f"{account_number}.csv")



    return "Done"




@shared_task
def set_up_108_task(trans_data: list):

    float_data = AccountSystem.get_dynamic_float_account(from_wallet_type="FLOAT", from_provider_type="VFD")
    float_account_num = float_data.account_number
    float_comm_num = float_data.account_number

    new_trans_data = []
    for id in trans_data:
        try:
            trans = TransferVerificationObject.objects.get(id=id)
            new_trans_data.append(trans)
        except:
            pass

    for trans_verf in new_trans_data:
        error_message = []

        trans_verf: TransferVerificationObject
        transaction_inst = trans_verf.transaction_instance
        liberty_reference = trans_verf.liberty_reference


        if trans_verf.transaction_leg == "COMMISSIONS":
            # Source Nuban
            if not trans_verf.source_nuban:
                if transaction_inst.transaction_sub_type == "MAIN_TRSF_COMM":
                    trans_verf.source_nuban = Escrow.objects.get(escrow_id=transaction_inst.escrow_id).user_account_number
                else:
                    trans_verf.source_nuban = float_account_num

            # Beneficiary Nuban
            if not trans_verf.beneficiary_nuban:
                trans_verf.beneficiary_nuban = float_comm_num




        if trans_verf.transaction_leg == "INFLOW_TO_FLOAT":
            if not trans_verf.source_nuban:
                trans_verf.source_nuban = transaction_inst.source_nuban

            if not trans_verf.beneficiary_nuban:
                trans_verf.beneficiary_nuban = transaction_inst.beneficiary_nuban


        if trans_verf.transaction_leg == "RE_INTERNAL":
            if not trans_verf.source_nuban:
                trans_verf.source_nuban = transaction_inst.source_nuban

            if not trans_verf.beneficiary_nuban:
                trans_verf.beneficiary_nuban = float_account_num



        if not trans_verf.bank_code:
            trans_verf.bank_code = transaction_inst.beneficiary_bank_code


        if trans_verf.transaction_leg not in ["INFLOW_TO_FLOAT", "COMMISSIONS", "RE_INTERNAL"]:
            response = {
                "error": "545",
                "message": "Transfer Leg is not in INFLOW_TO_FLOAT or COMMISSIONS"
            }
            error_message.append(response)




        check_status = VFDBank.vfd_transaction_verification_handler(reference=liberty_reference, fail=True)
        check_status_code = check_status["status"]

        if check_status_code == "108":
            trans_verf.trans_status_code = check_status_code
        else:
            trans_verf.trans_status_code = check_status["data"]["transactionStatus"]

        trans_verf.save()

        if error_message:
            continue


    function_name = set_up_108_task.__name__
    send_email(email=float_data.user.email, passcode=f"done processing {function_name} for {trans_data}")

    return "Done"



@shared_task
def resolve_108_task(trans_data: list=None, status_codes: list=["108"], days_back: int=1):

    float_data = AccountSystem.get_dynamic_float_account(from_wallet_type="FLOAT", from_provider_type="VFD")

    if not trans_data:
        yesterday = (timezone.now() - timezone.timedelta(days=days_back)).date() if days_back > 0 else timezone.now().date()
        new_trans_data = TransferVerificationObject.objects.filter(trans_status_code="108", transaction_type="SEND_LIBERTY_COMMISSION", date_added__date=yesterday, transaction_ver_status__in=["NOT_INITIATED"])

    else:
        new_trans_data = []
        for id in trans_data:
            try:
                trans = TransferVerificationObject.objects.get(id=id)
                new_trans_data.append(trans)
            except:
                pass


    for trans_verf in new_trans_data:
        error_message = []

        trans_verf: TransferVerificationObject
        from_acct_no = trans_verf.source_nuban
        to_acct_no = trans_verf.beneficiary_nuban
        amount = trans_verf.amount
        bank_code = trans_verf.bank_code
        escrow_id = trans_verf.escrow_id
        liberty_reference = trans_verf.liberty_reference
        transaction_inst = trans_verf.transaction_instance
        trans_user = transaction_inst.user


        if trans_verf.trans_status_code not in status_codes:
            response = {
                "error": "545",
                "message": "Transfer status not status codes"
            }

            error_message.append(response)


        if trans_verf.transaction_leg == "EXTERNAL":
            response = {
                "error": "545",
                "message": "Transfer Leg is external"
            }
            error_message.append(response)



        if not to_acct_no or not from_acct_no or not bank_code:
            response = {
                "error": "545",
                "message": "either there is no source nuban or no beneficiary nuban or no bank code"
            }
            error_message.append(response)


        check_status = VFDBank.vfd_transaction_verification_handler(reference=liberty_reference, fail=True)
        check_status_code = check_status["status"] if check_status["status"] == "108" else check_status["data"]["transactionStatus"]

        if check_status_code == "00":
            response = {
                "error": "545",
                "message": "trans has been sent before now. already verified"
            }
            error_message.append(response)


        if error_message:
            continue


        response = AccountSystem.handle_out_of_books(
            from_account=from_acct_no,
            to_account=to_acct_no,
            amount=amount,
            escrow_id=escrow_id,
            user_bvn_number=trans_user.bvn_number,
            transaction_instance=transaction_inst,
            liberty_reference=liberty_reference
        )




    function_name = resolve_108_task.__name__
    send_email(email=float_data.user.email, passcode=f"done processing {function_name} for {trans_data}")

    return "Done"




@shared_task
def reverify_all_auto_comms():
    from django.db.models import QuerySet
    date_back = datetime(2023, 8, 31)

    cache_key = 'pending_comm_qs'
    queryset = cache.get(cache_key)

    if queryset is None or (isinstance(queryset, QuerySet) and queryset.count() < 1):
        queryset = TransferVerificationObject.objects.filter(transaction_ver_status="PENDING", transaction_type="SEND_LIBERTY_COMMISSION", date_added__date__lte=date_back)

        # Cache the result with the cache key
        cache.set(cache_key, queryset)




    trans_data = queryset.order_by("-id")[:200]
    queryset = queryset.exclude(pk__in=trans_data.values('pk'))
    cache.set(cache_key, queryset)
    log_info(str(queryset))
    log_info(str(queryset.count()))
    log_info(str(trans_data))

    for transaction_verf in trans_data:
        transaction_verf: TransferVerificationObject
        liberty_reference = str(transaction_verf.liberty_reference)

        log_info(str(transaction_verf.date_added))

        if transaction_verf.trans_status_code == "108":
            # print("::::entered hererrrreeeee:::::", liberty_reference)
            # verify_trans = VFDBank.vfd_transaction_verification_handler(reference=liberty_reference)
            # print("::::entered :::::", verify_trans)
            # if verify_trans["status"] == "108":
            transaction_verf.transaction_ver_status = "NOT_INITIATED"
            # transaction_verf.verification_payload = verify_trans
            transaction_verf.save()

        else:
            verification_instance = dict(
                transaction_instance = transaction_verf.transaction_instance,
                user_id = transaction_verf.user_id,
                user_email = transaction_verf.user_email,
                account_provider = transaction_verf.account_provider,
                transaction_leg = transaction_verf.transaction_leg,
                transaction_type = transaction_verf.transaction_type,
                timestamp = str(datetime.now()),
                escrow_id = transaction_verf.escrow_id,
                amount = transaction_verf.amount,
                liberty_reference = liberty_reference,
                is_test = True if settings.ENVIRONMENT == "development" else False
            )

            verf_pending_trans = TransferVerificationObject.create_verfication_check(verification_instance, instant=True)


    return list(queryset.values('date_added__month', 'date_added__year').distinct())




# @shared_task
# def flush_accounts_task(exclude_accts: list=None, include_accts: list=None):
@shared_task
def flush_accounts_task(queryset=list):
    import pandas as pd

    float_account = AccountSystem.get_float_account(from_wallet_type=AccountTypes.FLOAT, from_provider_type=AccountProviders.VFD_PROVIDER)
    table_needed = []
    count = 0


    all_trans_count = len(queryset)
    log_info(str(all_trans_count))
    for acct in queryset:

        user_id =  acct["user_id"]
        user_email =  acct["user_email"]
        account_number = acct["account_number"]
        account_name = acct["account_name"]
        user_bvn = acct["user_bvn"]


        log_info(f"{count}, remaining: {all_trans_count - count} account_number")

        data = {
            "user_email": user_email,
            "account_name": account_name,
            "account_number": account_number,
            "error": None
        }

        try:
            vfd_balance = VFDBank.get_vfd_float_balance(account_number=account_number)

            if vfd_balance is not None and vfd_balance > 0:

                data["account_balance"] = vfd_balance

                pending_trans = Transaction.objects.filter(user_id=user_id, transaction_type__in=["SEND_BANK_TRANSFER", "FUND_BANK_TRANSFER", "REVERSAL_BANK_TRANSFER"], status__in=["PENDING", "IN_PROGRESS"])

                if pending_trans:
                    data["pending_trans"] = list(pending_trans.values_list("id", flat=True))
                    data["has_pending"] = True
                    data["response"] = None

                # else:
                liberty_reference = Transaction.create_liberty_reference(suffix="SWEEP")
                handle_rev = AccountSystem.handle_out_of_books(
                    from_account=account_number,
                    to_account=float_account.account_number,
                    amount=vfd_balance,
                    escrow_id=None,
                    user_bvn_number=user_bvn,
                    transaction_instance=None,
                    liberty_reference=liberty_reference,
                    narration="cleanup"
                )

                vfd_balance = VFDBank.get_vfd_float_balance(account_number=account_number)
                data["pending_trans"] = None
                data["has_pending"] = False
                data["response"] = handle_rev


        except Exception as err:
            data["error"] = f"{err}"


        finally:
            table_needed.append(data)
            count += 1



    base_file_name = "flushed_accts.csv"
    counter = 1
    new_file_name = base_file_name

    while os.path.exists(new_file_name):
        counter += 1
        new_file_name = f"flushed_accts_{counter}.csv"


    df = pd.DataFrame(table_needed)
    df.to_csv(new_file_name)


    return f"{queryset}"


@shared_task
def update_transaction_summary(escrow_id: str):
    from accounts.serializers import ReturnEscrowTransactions
    escrow_instance = Escrow.objects.get(escrow_id=escrow_id)
    ReturnEscrowTransactions.get_trans_with_escrow(escrow_instance)

    return "Done"


@shared_task
def create_update_pending_trx():
    list_display = ["id", "date_created", "last_updated", "transaction_type", "amount", "status", "user_email", "beneficiary_account_name", "beneficiary_nuban", "transaction_id", "transaction_leg", "escrow_id", "liberty_reference", "unique_reference", "narration"]


    send_buddy_types = ["SEND_BUDDY", "SEND_LOTTO_WALLET", "SEND_AJO_WALLET", "SEND_AJO_LOANS" "SEND_COLLECTION_ACCOUNT", "LOTTO_PLAY", "LIBERTY_LIFE_PAYMENT"]
    transfer_types = ["SEND_BANK_TRANSFER", "SEND_LIBERTY_COMMISSION", "REVERSAL_BANK_TRANSFER_IN", "REVERSAL_BANK_TRANSFER", "FUND_BANK_TRANSFER", "FUND_PAYSTACK", "SEND_BACK_TO_FLOAT_TRANSFER", "RE_FAILED_TRANSFER_IN"]
    fund_buddy_types = ["FUND_BUDDY", "FUND_LOTTO_WALLET", "FUND_AJO_WALLET", "FUND_AJO_LOANS", "FUND_COLLECTION_ACCOUNT"]
    card_trx_types = ["CARD_TRANSACTION_FUND", "CARD_TRANSACTION_FUND_TRANSFER"]
    bills_type = ["BILLS_AND_PAYMENT", "BILLS_AND_PAYMENT_REVERSAL", "AIRTIME_PIN", "AIRTIME_PIN_REVERSAL"]

    status_types = ["SUCCESSFUL", "FAILED", "REVERSED", "IGNORE_HISTORY"]

    all_trx_types = send_buddy_types + transfer_types + fund_buddy_types + card_trx_types + bills_type

    start_date = datetime(2024, 1, 1)

    PendingTransaction.objects.all().delete()
    all_trx = Transaction.objects.exclude(status__in=status_types).filter(date_created__gte=start_date).filter(transaction_type__in=all_trx_types).only(*list_display)

    for trx in all_trx:
        try:
            escrow_instance = Escrow.objects.get(escrow_id=trx.escrow_id)
        except Exception:
            escrow_instance = None

        PendingTransaction.objects.create(
            trx_id = trx.id,
            trx_inst = trx,
            trx_date_created = trx.date_created,
            trx_last_updated = trx.last_updated,
            trx_type = trx.transaction_type,
            trx_amount = trx.amount,
            trx_status = trx.status,
            user_email = trx.user_email,
            trx_verf_obj = trx.verifiable_trans.all().last(),
            beneficiary_account_name = trx.beneficiary_account_name,
            beneficiary_nuban = trx.beneficiary_nuban,
            trx_transaction_id = trx.transaction_id,
            trx_leg = trx.transaction_leg,
            liberty_reference = trx.liberty_reference,
            escrow = escrow_instance,
            unique_reference = trx.unique_reference
        )



    return "Done"


@shared_task
def process_pending_callbacks():
    pending_callbacks = CallbackSending.objects.select_for_update(skip_locked=True).filter(
        received=False,
        not_received=False
    )

    for callback in pending_callbacks:
        with transaction.atomic():
            callback = CallbackSending.objects.select_for_update().get(id=callback.id)
            transaction_obj = callback.transaction

            try:
                wallet = WalletSystem.objects.get(wallet_id=transaction_obj.wallet_id)

                # Check if transaction is successful
                if transaction_obj.status == Transaction.SUCCESSFUL:
                    try:
                        response = requests.post(
                            wallet.callback_url,
                            json={
                                "transaction_id": str(transaction_obj.transaction_id),
                                "amount": transaction_obj.amount,
                                "status": "success",
                                "timestamp": now().isoformat()
                            },
                            timeout=10
                        )

                        callback.callback_response = response.text

                        if response.status_code == 200:
                            callback.received = True

                            transaction_obj.callback_sent = True
                            transaction_obj.callback_response = response.text
                            transaction_obj.save(update_fields=['callback_sent', 'callback_response'])
                        else:
                            callback.not_received = True

                    except requests.RequestException as e:
                        callback.not_received = True
                        callback.callback_response = str(e)
                else:
                    callback.not_received = True

                callback.save()

            except WalletSystem.DoesNotExist:
                callback.not_received = True
                callback.save()
